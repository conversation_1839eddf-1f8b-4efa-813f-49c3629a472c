const mongoose = require("mongoose");
const bcrypt = require("bcrypt");
const ActionLogModel = new (require("../../Models/ActionLogModel"))();

const {
	VERIFICATION_STATUS,
	GENDER,
	USER_TYPE,
	LOG_ACTION_TYPE
} = require("../../Configs/constants")

const VerificationSchema = new mongoose.Schema(
	{
		verificationId: {
			type: String,
			required: true,
		},
		sessionToken: {
			type: String,
			required: true,
		},
		url: {
			type: String,
			required: true,
		},
		reason: {
			type: String,
		},
		document: {
			type: {
				type: String,
			},
			state: {
				type: String,
			},
			number: {
				type: String,
			},
			country: {
				type: String,
			},
			validFrom: {
				type: String,
			},
			validUntil: {
				type: String,
			},
		}
	},
	{
		_id: false
	}
)

const UserSchema = new mongoose.Schema(
	{
		roleId: {
			type: mongoose.Schema.Types.ObjectId,
			// required: true,
			ref: 'roles',
		},
		firstName: {
			type: String,
			required: true,
			trim: true,
		},
		lastName: {
			type: String,
			required: true,
			trim: true,
		},
		email: {
			type: String,
			trim: true,
			required: true,
			lowercase: true,
		},
		password: {
			type: String,
			required: false,
			trim: true,
			select: false
		},
		profilePicture: {
			type: String,
			trim: true
		},
		countryCode: {
			type: String,
			trim: true,
		},
		phoneNumber: {
			type: String,
			trim: true,
		},
		address: {
			type: String,
			trim: true,
		},
		country: {
			type: String,
			trim: true,
		},
		countryId: {
			type: String,
			trim: true,
		},
		state: {
			type: String,
			trim: true,
		},
		city: {
			type: String,
			trim: true,
		},
		cityId: {
			type: String,
			trim: true,
		},
		zipCode: {
			type: String,
			trim: true,
		},
		sentMoneyToCountry: {
			type: String,
			trim: true,
		},
		region: {
			type: String,
			trim: true,
			required: true,
			default: process.env.REGION || 'default',
			index: true // Add index for efficient querying
		},
		regionCode: {
			type: String,
			trim: true,
			uppercase: true,
			default: process.env.REGION_CODE || 'DEFAULT'
		},
		birthDate: {
			type: String,
		},
		facebookId: {
			type: String,
			default: null
		},
		googleId: {
			type: String,
			default: null
		},
		appleId: {
			type: String,
			default: null
		},
		kycStatus: {
			type: String,
			required: true,
			trim: true,
			enum: Object.values(VERIFICATION_STATUS),
			default: VERIFICATION_STATUS.PENDING
		},
		gender: {
			type: String,
			required: false,
			trim: true,
			enum: Object.values(GENDER),
		},
		verification: {
			type: VerificationSchema,
			select: false
		},
		isActive: {
			type: Boolean,
			required: true,
			default: true,
		},
		createdBy: {
			type: mongoose.Schema.Types.ObjectId,
			select: false
		},
		updatedBy: {
			type: mongoose.Schema.Types.ObjectId,
			select: false
		},
		deletedAt: {
			type: Date,
			select: false
		},
		nationalId: {
			type: String
		},
		nationality: {
			type: String,
		},
		country: {
			type: String
		},
		streetAddress: {
			type: String,
		},
		state: {
			type: String,
		},
		city: {
			type: String,
		},
		zipCode: {
			type: String,
		},
		isUserImported: {
			type: Boolean,
			default: false
		},
		isCreatedByAdmin: {
			type: Boolean,
			default: false
		},
		isAppNotificationEnabled: {
			type: Boolean,
			default: true
		},
		isEmailNotificationEnabled: {
			type: Boolean,
			default: true
		},
		agentId: {
			type: String
		},
		userSource: {
			type: String
		},
		nearestRelativeName: {
			type: String
		},
		nearestRelativeEmail: {
			type: String
		},
		nearestRelativePhone: {
			type: String
		},
		nearestRelativeCountryCode: {
			type: String
		},
		billingAddress: {
			type: String,
			trim: true,
		},
		billingState: {
			type: String,
			trim: true,
		},
		billingCity: {
			type: String,
			trim: true,
		},
		billingZipCode: {
			type: String,
			trim: true,
			required: false,
		},
	}
)

UserSchema.pre("save", async function (next) {
	if (this.isModified('password')) {
		this.password = await bcrypt.hash(this.password, 12);
	}

	next();
});

UserSchema.pre("insertMany", async function (next, docs) {
	for (let user of docs) {
		user.password = await bcrypt.hash(user.password, 12)
	}
	next();
});

UserSchema.post("insertMany", async function (docs) {
	const userLogs = []
	for (let user of docs) {
		userLogs.push(
			ActionLogModel.createActionLog(
				{
					entity: "user",
					description: `${user.firstName} ${user.lastName} user is created`,
					entityId: user._id,
					firstName: user.firstName,
					lastName: user.lastName,
					email: user.email,
					action: LOG_ACTION_TYPE.CREATE,
				},
				user.createdBy
			)
		);
	}
	await Promise.allSettled(userLogs);
});

UserSchema.index({ firstName: 1, lastName: 1, email: 1, phoneNumber: 1 });
UserSchema.index({ createdAt: 1 });

module.exports = mongoose.model("users", UserSchema);
