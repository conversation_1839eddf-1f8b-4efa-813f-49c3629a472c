const mongoose = require('mongoose');

const MediaSchema = new mongoose.Schema(
    {
        id: {
            type: String,
        },
        name: {
            type: String,
        },
        context: {
            type: String,
        },
        timestamp: {
            type: Date,
            default: null
        },
        size: {
            type: Number,
        },
        mimetype: {
            type: String,
        },
        sessionId: {
            type: String,
        },
        url: {
            type: String,
        }
    }
);

const VeriffDocumentSchema = new mongoose.Schema({
    status: {
        type: String,
    },
    verificationId: {
        type: String,
    },
    userId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true,
        ref:"users"
    },
    documentName:{
        type: String,
    },
    images: [MediaSchema],
    videos: [MediaSchema],
    documentType:{
        type: String,
    },
    documentNumber:{
        type: String,
    },
    documentIssuer: {
        type: String,
    },
    documentIssueDate:{
        type:Date,
    },
    documentExpireDate:{
        type: Date,
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        select: false
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        select: false
    },
    deletedAt: {
        type: Date,
        select: false
    }
});

const Session = mongoose.model('veriff_documents', VeriffDocumentSchema);

module.exports = Session;
