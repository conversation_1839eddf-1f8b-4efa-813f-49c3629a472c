const mongoose = require("mongoose");

const {
    DEVICE_TYPE,
    LOGIN_WITH
} = require("../../Configs/constants")

const UserSessionSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        authToken: {
            type: String,
            trim: true,
            required: true,
        },
        deviceId: {
            type: String,
            trim: true,
            required: true,
        },
        deviceType: {
            type: String,
            trim: true,
            required: true,
            enum: Object.values(DEVICE_TYPE)
        },
        loginWith: {
            type: String,
            trim: true,
            required: true,
            enum: Object.values(LOGIN_WITH)
        },
        timezone: {
            type: String,
            trim: true,
            required: true,
        },
        appVersion: {
            type: String,
            trim: true,
            required: true,
        },
        authToken: {
            type: String,
            trim: true,
            required: true,
        },
    }
)

module.exports = mongoose.model("user_sessions", UserSessionSchema);
