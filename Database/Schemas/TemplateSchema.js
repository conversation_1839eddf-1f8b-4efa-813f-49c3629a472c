const { default: mongoose, Schema } = require("mongoose");
const { TEMPLATE_TYPE } = require("../../Configs/constants");

const TemplateSchema = new Schema(
    {
        slug: {
            type: String,
            required: true,
            unique: true,
            trim: true
        },
        type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(TEMPLATE_TYPE),
        },
        title: {
            type: String,
            required: true,
            trim: true
        },
        content: {
            type: String,
            required: true,
            trim: true
        },
        json: {
            type: Object,
            default: {}
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
    }
)

module.exports = mongoose.model("templates", TemplateSchema)
