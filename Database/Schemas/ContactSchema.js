const mongoose = require("mongoose");

const ContactUsSchema = new mongoose.Schema({
    firstName:{
        required: true,
        type: String,
        trim: true,
    },
    lastName:{
        required: true,
        type: String,
        trim: true,
    },
    email: {
        required: true,
        type: String,
        trim: true,
    },
    countryCode: {
        required: true,
        type: String,
        trim: true
    },
    phoneNumber: {
        required: true,
        type: String,
        trim: true  
    },
    message: {
        trim: true,
        type: String,
        required: true,
    },
    privacyPolicy:{
        type: Boolean,
        default: true
    }
});


module.exports = mongoose.model("contactUs",ContactUsSchema);