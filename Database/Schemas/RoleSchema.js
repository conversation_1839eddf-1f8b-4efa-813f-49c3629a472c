const mongoose = require("mongoose");
const slugify = require("slugify")

const RoleSchema = new mongoose.Schema(
    {
        slug: {
            type: String,
            required: true,
            trim: true
        },
        name: {
            type: String,
            required: true,
            trim: true,
        },
        canAccessCMS: {
            type: Boolean,
            required: true,
            default: false
        },
        isModify:{
            type: Boolean,
            default: true
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        deletedAt: {
            type: Date
        }
    }
)

RoleSchema.pre("validate", function(next){
    console.log(this)
    this.slug = slugify(this.name);
    next();
})

module.exports = mongoose.model("roles", RoleSchema);
