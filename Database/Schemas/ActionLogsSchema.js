const { default: mongoose, Schema } = require("mongoose");
const { LOG_ACTION_TYPE } = require("../../Configs/constants");

const ActionLogsSchema = new Schema(
    {
        entity: {
            type: String,
            required: true,
            trim: true
        },
        entityId: {
            type: mongoose.Schema.Types.ObjectId,
        },
        action: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(LOG_ACTION_TYPE),
        },
        firstName: {
			type: String,
			trim: true,
		},
		lastName: {
			type: String,
			trim: true,
		},
        email: {
			type: String,
			trim: true,
		},
        description: {
            type: String,
            trim: true
        },
        performedBy: { 
            type: mongoose.Schema.Types.ObjectId, 
            required: true 
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
    }
)

module.exports = mongoose.model("action_logs", ActionLogsSchema)
