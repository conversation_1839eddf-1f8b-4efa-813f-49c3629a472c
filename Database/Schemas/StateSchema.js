const mongoose = require('mongoose');

const StateSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true,
        unique: true,
        trim: true,
    },
    code: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        uppercase: true,
        minlength: 2,
        maxlength: 2,
    },
    isActive: {
        type: Boolean,
        required: true,
        default: false,
        select: false,
    },
});

module.exports = mongoose.model('states', StateSchema);
