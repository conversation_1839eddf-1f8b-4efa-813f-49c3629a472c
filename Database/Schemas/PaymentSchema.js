const { default: mongoose, Schema } = require("mongoose");

const PaymentSchema = new Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        recipientId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        cardId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        transactionId: {
            type: String,
            required: true,
            trim: true,
            select: false
        },
        refundReferenceTransactionId: {
            type: String,
            trim: true,
            select: false
        },
        amount: {
            type: Number,
            required: true,
            trim: true,
        },
        type: {
            type: String,
            required: true,
            trim: true,
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        deletedAt: {
            type: Date,
            select: false
        }
    }
)

module.exports = mongoose.model("payments", PaymentSchema)
