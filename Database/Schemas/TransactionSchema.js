const { default: mongoose, Schema } = require("mongoose");

const {
    PAYMENT_STATUS_TYPE,
} = require("../../Configs/constants")

const TransactionStatusSchema = new mongoose.Schema(
    {
        status: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(PAYMENT_STATUS_TYPE),
        },
        date: {
            type: Date,
            required: true,
        },
    }
);

const TransactionSchema = new Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "users"
        },
        recipientId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "recipients"
        },
        cardId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "user_cards"
        },
        paymentId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        transactionRefNo: {
            type: String,
            required: true,
            trim: true,
            select: false
        },
        transactionId: {
            type: String,
            required: true,
            trim: true,
        },
        bankReferenceNumber:{
            type: String,
            trim: true,
        },
        myCashReferenceNumber:{
            type: String,
            trim: true,
        },
        senderKey: {
            type: String,
            trim: true,
        },
        receiverKey: {
            type: String,
            trim: true,
        },
        sourceBranchkey: {
            type: Number,
            required: true,
        },
        stateName: {
            type: String,
            required: true,
        },
        sourceCountryName:{
            type: String,
            required: true,
        },
        sourceCountryId:{
            type: Number,
            required: true,
        },
        sourceCityName:{
            type: String,
            required: true,
        },
        sourceCityId:{
            type: Number,
            required: true,
        },
        paymentCityName: {
            type: String,
            required: true,
        },
        sourceCountry: {
            type: Number,
            required: true,
        },
        senderFName: {
            type: String,
            required: true,
            trim: true,
        },
        senderLName: {
            type: String,
            required: true,
            trim: true,
        },
        senderMName: {
            type: String,
            required: false,
            trim: true,
        },
        senderMobileCountryCode: {
            type: String,
            required: true,
        },
        senderMobile: {
            type: String,
            required: true,
        },
        senderTelephone: {
            type: String,
            required: false,
        },
        senderAddress: {
            type: String,
            required: false,
        },
        senderDOB: {
            type: String,
            required: true,
        },
        senderNationality: {
            type: String,
            required: true,
        },
        senderCity: {
            type: String,
            required: true,
        },
        senderGender: {
            type: String,
            required: true,
        },
        senderPostalCode: {
            type: String,
            required: true,
        },
        senderEmail: {
            type: String,
            required: false,
        },
        senderOccupation: {
            type: String,
            required: false,
        },
        serviceId: {
            type: Number,
            required: true,
        },
        serviceOperatorId: {
            type: Number,
            required: true,
        },
        destinationCountryId: {
            type: Number,
            required: true,
        },
        beneficiaryFirstName: {
            type: String,
            required: true,
        },
        beneficiaryMiddleName: {
            type: String,
            required: false,
        },
        beneficiaryLastName: {
            type: String,
            required: true,
        },
        beneficiaryMobileCountryCode: {
            type: String,
            required: true,
        },
        beneficiaryMobile: {
            type: String,
            required: true,
        },
        beneficiaryTelephone: {
            type: String,
            required: false,
        },
        beneficiaryAddress: {
            type: String,
            required: false,
        },
        totalAmount: {
            type: mongoose.Schema.Types.Decimal128,
            required: true,
        },
        totalCommission: {
            type: String,
            required: false,
        },
        serviceTax: {
            type: String,
            required: false,
        },
        sendingCurrency: {
            type: String,
            required: true,
        },
        payoutMethod: {
            type: String,
            required: true,
        },
        exchangeRate:{
            type: String,
            required: true,
        },
        currencyToReceive: {
            type: String,
            required: true,
        },
        amountToReceive: {
            type: mongoose.Schema.Types.Decimal128,
            required: true,
        },
        purpose: {
            type: String,
            required: true,
        },
        sourceOfIncome: {
            type: String,
            required: true,
        },
        senderRemarks: {
            type: String,
            required: true,
        },
        userCreated: {
            type: String,
            required: true,
        },
        transactionReferenceNo: {
            type: String,
            required: true,
        },
        collectionMode: {
            type: String,
            required: true,
        },
        documentName: {
            type: String,
            required: true,
        },
        documentNumber: {
            type: String,
            required: true,
        },
        Issuer: {
            type: String,
            required: true,
        },
        dateOfIssue: {
            type: String,
            required: true,
            validate: {
                validator: function (value) {
                    return /^\d{2}\/\d{2}\/\d{4}$/.test(value);
                },
                message: "dateofIssue must be in the format mm/dd/yyyy"
            }
        },
        transactionDate: {
            type: String,
            required: true,
        },
        dateOfExpire: {
            type: String,
            required: true,
            validate: {
                validator: function (value) {
                    return /^\d{2}\/\d{2}\/\d{4}$/.test(value);
                },
                message: "dateofExpire must be in the format mm/dd/yyyy"
            }
        },
        destinationCityId: {
            type: String,
            required: false,
        },
        Bankname: {
            type: String,
            required: false,
        },
        beneficiaryBankAccNo: {
            type: String,
            required: false,
        },
        transactionStatus: [TransactionStatusSchema],
        paymentStatus: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(PAYMENT_STATUS_TYPE),
            default: PAYMENT_STATUS_TYPE.INITIATED
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        deletedAt: {
            type: Date,
            select: false
        }
    }
)

TransactionSchema.index({ transactionId: 1 })
TransactionSchema.index({ createdAt: 1 });
TransactionSchema.index({ destinationCountryId: 1 });
module.exports = mongoose.model("transaction", TransactionSchema)
