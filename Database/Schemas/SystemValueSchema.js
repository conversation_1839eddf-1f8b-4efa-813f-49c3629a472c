const { default: mongoose, Schema } = require("mongoose");

const SystemValueSchema = new Schema(
    {
        slug: {
            type: String,
            required: true,
            trim: true
        },
        title: {
            type: String,
            required: true,
            trim: true
        },
        description: {
            type: String,
            trim: true
        },
        value: {
            type: Schema.Types.Mixed,
            required: true,
            /*
                Since Mixed is a schema-less type, you can change the value to anything else you like, but Mongoose loses the ability to auto detect and save those changes. To tell Mongoose that the value of a Mixed type has changed, you need to call doc.markModified(path)
                https://mongoosejs.com/docs/schematypes.html#mixed
            */
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
    }
)

module.exports = mongoose.model("system_values", SystemValueSchema)
