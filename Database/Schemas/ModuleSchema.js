const mongoose = require("mongoose");
const slugify = require("slugify");

const ModuleSchema = new mongoose.Schema({
    name: {
        type: String,
        required: true
    },
    slug:{
        type: String,
    },
    role:{
      type: mongoose.Schema.ObjectId,
      required: true,
      ref: "roles"
    },
    add: {
        type: Boolean,
        default: false
    },
    edit: {
        type: Boolean,
        default: false
    },
    delete: {
        type: Boolean,
        default: false
    },
    view: {
        type: Boolean,
        default: false
    },
    deletedAt: {
        type: Date,
        select: false
    }
});

ModuleSchema.pre("save", function(next){
    this.slug = slugify(this.name);
    next();
});

module.exports = mongoose.model("module", ModuleSchema);