const { default: mongoose, Schema } = require("mongoose");

const {
    STATIC_PAGE_SLUG
} = require("../../Configs/constants")

const StaticPageSchema = new Schema(
    {
        slug: {
            type: String,
            required: true,
            unique: true,
            trim: true,
            enum: Object.values(STATIC_PAGE_SLUG),
        },
        slug: {
            type: String,
            required: true,
            trim: true
        },
        title: {
            type: String,
            required: true,
            trim: true
        },
        content: {
            type: String,
            required: true,
            trim: true
        },
        updated_by: {
            type: mongoose.Schema.Types.ObjectId,
        },
    }
)

module.exports = mongoose.model("static_pages", StaticPageSchema)
