const { default: mongoose, Schema } = require("mongoose");
const { CARD_TYPE } = require("../../Configs/constants");


const UserCardSchema = new Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        name: {
            type: String,
            required: true,
            trim: true
        },
        type: {
            type: String,
            required: true,
            trim: true,
            enum: Object.values(CARD_TYPE),
            default: CARD_TYPE.DEBIT
        },
        lastDigits: {
            type: String,
            required: true,
            trim: true,
            minlength: 4,
            maxlength: 4,
        },
        token: {
            type: String,
            required: true,
            trim: true,
            select: false
        },
        isPrimary: {
            type: Boolean,
            required: true,
            default: false,
            select: false,
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        deletedAt: {
            type: Date,
            select: false
        }
    }
)

module.exports = mongoose.model("user_card", UserCardSchema)
