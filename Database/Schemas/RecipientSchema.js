const mongoose = require("mongoose");

const RecipientSchema = new mongoose.Schema(
    {
        userId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
        },
        email: {
            type: String,
            trim: true,
            required: false,
            lowercase: true,
        },
        address: {
            type: String,
            trim: true,
            required: true,
        },
        state: {
            type: String,
            trim: true,
            required: true,
        },
        country: {
            type: String,
            trim: true,
            required: true,
        },
        countryId: {
            type: String,
            trim: true,
            required: true,
        },
        city: {
            type: String,
            trim: true,
            required: true,
        },
        cityId: {
            type: String,
            trim: true,
            required: true,
        },
        zipCode: {
            type: String,
            trim: true,
            required: true,
        },
        countryCode: {
            type: String,
            trim: true,
            required: true,
        },
        phoneNumber: {
            type: String,
            trim: true,
            required: true,
        },
        firstName: {
            type: String,
            trim: true,
            required: true,
        },
        middleName: {
            type: String,
            trim: true,
        },
        lastName: {
            type: String,
            trim: true,
            required: true,
        },
        bankName: {
            type: String,
            trim: true,
        },
        accountNumber: {
            type: String,
            trim: true,
        },
        billingAddress: {
            type: String,
            trim: true,
            required: true,
        },
        billingState: {
            type: String,
            trim: true,
            required: true,
        },
        billingCity: {
            type: String,
            trim: true,
            required: true,
        },
        billingZipCode: {
            type: String,
            trim: true,
            required: false,
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        deletedAt: {
            type: Date,
            select: false
        }
    }
)

module.exports = mongoose.model("recipients", RecipientSchema);