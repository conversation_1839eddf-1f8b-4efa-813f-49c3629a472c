const { default: mongoose, Schema } = require("mongoose");

const FaqSchema = new Schema(
    {
        question: {
            type: String,
            required: true,
            trim: true
        },
        answer: {
            type: String,
            required: true,
            trim: true
        },
        topics: {
            type: [String],
        },
        createdBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        updatedBy: {
            type: mongoose.Schema.Types.ObjectId,
            select: false
        },
        deletedAt: {
            type: Date,
            select: false
        }
    }
)

module.exports = mongoose.model("faqs", FaqSchema)
