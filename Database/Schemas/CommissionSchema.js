const mongoose = require("mongoose");

const {
    MULTIPLIER_TYPE,
    COMMISSION_SLUG
} = require("../../Configs/constants")

const CommissionSchema = new mongoose.Schema({
    slug: {
        type: String,
        required: true,
        trim: true,
        enum: Object.values(COMMISSION_SLUG),
    },
    name: {
        type: String,
        required: true,
    },
    feeRate: {
        type: Number,
        required: true,
    },
    multiplier: {
        type: String,
        required: true,
        trim: true,
        enum: Object.values(MULTIPLIER_TYPE),
    }
});

module.exports = mongoose.model("commissions", CommissionSchema);