const faqs = [
    {
        question: "Who uses your services?",
        answer: "Amal USA serves quite a diverse people, Our core customers include, immigrant communities from around the world. And they are mainly working mothers, fathers, truck drivers, professionals, non-for-profit organizations who would like to send money for both family support and humanitarian assistant, in areas where there are no banking systems.",
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        question: "How soon can my family expect the money I send?",
        answer: "",
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        question: "What payment methods do you accept?",
        answer: "",
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        question: "What documents do I need?",
        answer: "",
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        question: "What is SDN or blocked Persons?",
        answer: "",
        createdAt: new Date(),
        updatedAt: new Date()
    },
    {
        question: "How do I become an agent?",
        answer: "",
        createdAt: new Date(),
        updatedAt: new Date()
    },
]

db.faqs.insertMany(faqs)
