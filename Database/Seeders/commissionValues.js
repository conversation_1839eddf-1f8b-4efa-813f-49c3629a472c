const CommissionSchema = require("../Schemas/CommissionSchema")

const {
    MULTIPLIER_TYPE,
    COMMISSION_SLUG
} = require("../../Configs/constants");

module.exports = async function () {

    const commissionRate = [
        {
            slug: COMMISSION_SLUG.COMMISSION_FEE,
            name: "Commission Fee",
            feeRate: 5,
            multiplier: MULTIPLIER_TYPE.PERCENTAGE,
        },
        {
            slug: COMMISSION_SLUG.CARD_FEE,
            name: "Card Fee",
            feeRate: 2.7,
            multiplier: MULTIPLIER_TYPE.PERCENTAGE,
        },
        {
            slug: COMMISSION_SLUG.TRANSACTION_FEE,
            name: "Per Transaction Fee",
            feeRate: 0.3,
            multiplier: MULTIPLIER_TYPE.FLAT,
        }
    ]

    await CommissionSchema.insertMany(commissionRate);
}

