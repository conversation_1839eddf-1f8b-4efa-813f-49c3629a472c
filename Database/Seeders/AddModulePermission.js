const ModulePermission = require("../Schemas/ModuleSchema");
const Roles = require("../Schemas/RoleSchema");
const { USER_TYPE, MODULE_NAME_SLUG } = require("../../Configs/constants");
const mongoose = require("mongoose");
const slugify = require("slugify");


const seedFunction = async function () {
  const subAdminRoleId = await Roles.findOne(
    {
      slug: USER_TYPE.SUB_ADMIN,
    },
    "_id"
  );

  const adminRoleId = await Roles.findOne(
    {
      slug: USER_TYPE.ADMIN,
    },
    "_id"
  );

  const adminPermissionList = Object.values(MODULE_NAME_SLUG).map((moduleName) => ({
    role: new mongoose.Types.ObjectId(adminRoleId._id),
    name: moduleName,
    slug: slugify(moduleName),
    add: true,
    edit: true,
    view: true,
    delete: true,
  }));
  
  const subAdminPermissionList = Object.values(MODULE_NAME_SLUG)
  .filter((moduleName) => moduleName !== MODULE_NAME_SLUG.authorization)
  .map((moduleName) => ({
    role: new mongoose.Types.ObjectId(subAdminRoleId._id),
    name: moduleName,
    slug: slugify(moduleName),
    add: true,
    edit: true,
    view: true,
    delete: true,
  }));
  
  await ModulePermission.insertMany([...adminPermissionList, ...subAdminPermissionList]);
};

module.exports = seedFunction;
