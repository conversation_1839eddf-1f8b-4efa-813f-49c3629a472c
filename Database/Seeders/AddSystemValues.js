const systemValueSchema = require("../Schemas/SystemValueSchema");

async function seedSystemValue() {
  const systemValue = [
    {
      slug: "reasonForSendingMoney",
      title: "Reasons for sending money",
      value: [
        "Savings & Family support",
        "Personal Expenses",
        "Real Estate Investment",
        "Transfer to NRE/NRO Account",
        "Tuition Fees Support",
        "Hospitals or Healthcare Support",
        "Loan Payments",
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
    {
      slug: "sourceOfIncome",
      title: "Source of Income",
      value: [
        "Accountant",
        "Adult Foster Care",
        "Agent Owner",
        "Aircraft Mechanic",
        "Architectural Engineer",
        "Assembly Line",
        "Associate",
        "Assistant Cook",
        "Attorney",
        "Barber",
        "Banker",
        "Broker",
        "Bus Driver",
        "Business Analysts",
        "Childcare Provider",
        "Coding Instructor",
        "Community Advocate",
        "Construction Worker",
        "Cosmetic Store",
        "Cosmetologist",
        "Car Joker",
        "<PERSON>",
        "Cashier",
        "Certified Nurse Assistant",
        "Chief Cashier",
        "Cleaner",
        "Commissioner",
        "Company Ceo",
        "Compliance Officer",
        "Cook",
        "Customer Service",
        "Delivery",
        "Desktop Engineer",
        "Doctor",
        "Dentist",
        "Diplomat",
        "Director",
        "Dispatcher",
        "Distributor",
        "Due Diligence Analyst",
        "Electric Engineer",
        "Forklift Driver",
        "Family Doctor",
        "Grocery Shop Owner",
        "General Labor",
        "Health Care Coordinator",
        "Home Health Care Operator",
        "Hood Cleaning",
        "Human Resource",
        "Hair Dresser",
        "Healthcare",
        "House Wife",
        "Housewife",
        "Imam",
        "Insurance Agent",
        "Interpreter",
        "It Manager",
        "It Security Engineer",
        "Independent Taxi Driver",
        "Insulator",
        "Janitor",
        "Journalist",
        "Legal Assistant",
        "Liason Officer",
        "Locksmith",
        "Lab Technician",
        "Logistic Manager",
        "Logistic Staff",
        "Machine Operator",
        "Marketing Manager",
        "Marketing Staff",
        "Mechanic",
        "Medical Assistant",
        "Medical Transportation",
        "Office Assistant",
        "Operation Manager",
        "Owner Operator",
        "Personal Care Assistant",
        "Plant Maintenance",
        "Postal Mail Sorter",
        "Prod Supervisor",
        "Production Line",
        "Proprietor Childcare Center",
        "Public Health Officer",
        "Personal Care Aides Pca",
        "Personnel Worker Support",
        "Pharmacist",
        "Pipefitter",
        "Police Officer",
        "Quality Assurance",
        "Recruiting Manager",
        "Recycling Specialist",
        "Respiratory Therapist",
        "Retail Store Manager",
        "Radiologist",
        "Realtor",
        "Receptionist",
        "Registered Nurse",
        "Restaurant Owner",
        "Retired",
        "School Principal",
        "Self Employed",
        "Software Engineer",
        "Surgical Technician",
        "Sales Person",
        "Security Attendant",
        "Social Worker",
        "Software Developer",
        "Student",
        "Supervisor Warehouse",
        "Teller",
        "Taxi Driver",
        "Taylor",
        "Teacher",
        "Translator",
        "Travel Consultant",
        "Treasurer",
        "Truck Driver",
        "Truck Owner Operator",
        "Warehouse Labourer",
        "Women Clothing Shop Owner",
        "Waiter",
        "Warehouse",
        "Daycare",
        "Self Employment",
      ],
      createdAt: new Date(),
      updatedAt: new Date(),
    },
  ];

  await systemValueSchema.insertMany(systemValue);
}

module.exports = seedSystemValue;
