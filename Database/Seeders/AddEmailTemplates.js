const { EMAIL_TYPE_SLUG } = require("../../Configs/constants");
const TemplateSchema = require("../Schemas/TemplateSchema");

module.exports = async function () {
    const templateList = [
        // {
        //     slug: EMAIL_TYPE_SLUG.USER_KYC_VERIFICATION_DENIED,
        //     type: "EMAIL",
        //     title: "Verification Denied for {APP_NAME}",
        //     content: `
        //     <div>
        //         <p>
        //             Dear User,<br/>
        //             Your verification has been rejected by Veriff. due to {REASON}<br/><br/>
        //             Please create new account and upload proper documents!
        //         </p>
        //         <p>
        //             Best regards,<br/>
        //             {APP_NAME}
        //         </p>
        //     </div>
        //     `,
        //     createdAt: new Date(),
        //     updatedAt: new Date()
        // },
        // {
        //     slug: EMAIL_TYPE_SLUG.RESUBMIT_VERIFICATION,
        //     type: "EMAIL",
        //     title: "Verification process has not been completed for {APP_NAME}",
        //     content: `
        //     <div>
        //         <p>
        //             Dear User,<br/>
        //             Your Verification process has not been completed. due to {REASON}<br/><br/>
        //             Please resubmit proper documents!
        //         </p>
        //         <p>
        //             Best regards,<br/>
        //             {APP_NAME}
        //         </p>
        //     </div>
        //     `,
        //     createdAt: new Date(),
        //     updatedAt: new Date()
        // },
        // {
        //     slug: EMAIL_TYPE_SLUG.FORGOT_PASSWORD,
        //     type: "EMAIL",
        //     title: "Reset Password for {APP_NAME}",
        //     content: `
        //     <div>
        //         <p>
        //             Dear User,<br/>
        //             We received a request to reset your password.<br/><br/>
        //             Simply click the button below to reset your password
        //         </p>
        //         <a href="{URL} target="_self"
        //             style="display: inline-block;font-family:arial,helvetica,sans-serif;text-decoration: none;-webkit-text-size-adjust: none;text-align: center;color: #FFFFFF; background-color: #3AAEE0; border-radius: 4px; -webkit-border-radius: 4px; -moz-border-radius: 4px; width: auto; padding: 10px 20px; mso-border-alt: none;">
        //             <span style="line-height:120%;">
        //                 <span style="font-size: 14px; line-height: 16.8px;">
        //                     Reset Password
        //                 </span>
        //             </span>
        //         </a>
        //         <p>
        //             Best regards,<br/>
        //             {APP_NAME}
        //         </p>
        //     </div>
        //     `,
        //     createdAt: new Date(),
        //     updatedAt: new Date()
        // },
        // {
        //     slug: EMAIL_TYPE_SLUG.USER_TEMP_PASSWORD,
        //     type: "EMAIL",
        //     title: "Temporary Password for {APP_NAME}",
        //     content: `
        //     <div>
        //         <p>
        //             Dear User,<br/>
        //             Admin has added you in the {APP_NAME}.<br/><br/>
        //             Please use this temporary to login in the app change your password ASAP
        //         </p>
        //         <h4>{password}</h4>
        //         <p>Best regards,<br/>{APP_NAME}</p>
        //     </div>
        //     `,
        //     createdAt: new Date(),
        //     updatedAt: new Date()
        // },
        // {
        //     slug: EMAIL_TYPE_SLUG.QUARTERLY_REPORT,
        //     type: "EMAIL",
        //     title: "Quarterly Transaction Report {APP_NAME}",
        //     content: `
        //     <div>
        //         <p>
        //            Attached is your Quarterly Transaction Report for, 
        //            generated by {APP_NAME}. This report provides a summary of your transactions, 
        //            including total amounts, payment details, and other key insights.
        //         </p>
        //         <p>
        //             Best regards,<br/>
        //             {APP_NAME}
        //         </p>
        //     </div>
        //     `,
        //     createdAt: new Date(),
        //     updatedAt: new Date()
        // },
        // {
        //     slug: EMAIL_TYPE_SLUG.USER_PAYMENT_STATUS,
        //     type: "EMAIL",
        //     title: "Transaction Payment Status Update – {APP_NAME}",
        //     content: `
        //     <div>
        //         <p>
        //             We wanted to update you regarding your recent transaction on {APP_NAME}. Please find the status:{STATUS}
        //         </p>
        //         <p>
        //             Best regards,<br/>
        //             {APP_NAME}
        //         </p>
        //     </div>
        //     `,
        //     createdAt: new Date(),
        //     updatedAt: new Date()
        // },
        // {
        //     slug: EMAIL_TYPE_SLUG.USER_WELCOME,
        //     type: "EMAIL",
        //     title: "Welcome to – {APP_NAME}",
        //     content: `
        //     <div>
        //         <p>
        //           Successfully register on {APP_NAME}.
        //         </p>
        //         <p>
        //             Best regards,<br/>
        //             {APP_NAME}
        //         </p>
        //     </div>
        //     `,
        //     createdAt: new Date(),
        //     updatedAt: new Date()
        // },
        {
            slug: EMAIL_TYPE_SLUG.USER_ROLE_UPDATE,
            type: "EMAIL",
            title: "User role update",
            content: `
            <div>
                <p>
                   User role updated Successfully.
                </p>
                <p>
                    Best regards,<br/>
                    {APP_NAME}
                </p>
            </div>
            `,
            createdAt: new Date(),
            updatedAt: new Date()
        },
    ]
    await TemplateSchema.insertMany(templateList);
}