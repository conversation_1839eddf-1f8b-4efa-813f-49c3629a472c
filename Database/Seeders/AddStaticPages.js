const StaticPageSchema = require("../Schemas/StaticPageSchema")

const {
    STATIC_PAGE_SLUG
} = require("../../Configs/constants");

module.exports = async function () {

    const pages = [
        {
            slug: STATIC_PAGE_SLUG.PRIVACY_POLICY,
            title: "Privacy Policy",
            content: "Privacy Policy",
            createdAt: new Date(),
            updatedAt: new Date()
        },
        {
            slug: STATIC_PAGE_SLUG.TERMS_CONDITION,
            title: "Terms and conditions",
            content: "Terms and conditions",
            createdAt: new Date(),
            updatedAt: new Date()
        },
    ]

    await StaticPageSchema.insertMany(pages);
}

