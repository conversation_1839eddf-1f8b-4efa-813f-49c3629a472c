db.roles.insertOne({
    slug: "user",
    name: "User",
    canAccessCMS: false,
    createdAt: new Date(),
    updatedAt: new Date()
})

db.roles.insertOne({
    slug: "admin",
    name: "Admin",
    canAccessCMS: true,
    isModify: false,
    createdAt: new Date(),
    updatedAt: new Date()
})

db.roles.insertOne({
    name: "subAdmin",
    canAccessCMS: true,
    createdAt: new Date(),
    updatedAt: new Date()
});