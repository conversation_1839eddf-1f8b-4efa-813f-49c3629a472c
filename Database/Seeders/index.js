const path = require("path");
const dotenv = require("dotenv")
dotenv.config({
	path: path.resolve(__dirname, '../../.env'),
});
const mongoose = require("mongoose");
const AddEmailTemplates = require("./AddEmailTemplates");
const AddSystemValues = require("./AddSystemValues");
const commissionValue = require("./commissionValues")
const AddStaticPages = require("./AddStaticPages")
const rolesAndPermissions = require("./AddModulePermission")


mongoose
    .connect(process.env.MONGODB_URL, {
        dbName: process.env.DB_NAME,
    })
    .then(() => {
        const message = `database connected successfully :)`;
        console.log(message);
        Promise.all([AddEmailTemplates()])
        .then(() => {
            mongoose.connection.close();
            console.log("seeding is finished");
        })
        .catch((err) => {
            console.log("Unexpected Error...", err);
            process.exit(1);
        });
    })
    .catch((err) => console.log(err));
