const fs = require('fs');
const AWS = require('aws-sdk');
const path = require('path');
const multer = require('multer');

//Edit Image
const Sharp = require('sharp');

//Default Resolution
const IMAGE_RESIZE_RESOLUTION = parseInt(process.env.IMAGE_RESIZE_RESOLUTION) || 500

//Bucket Is Public
const isBucketPublic = process.env.AWS_BUCKET_TYPE === "public"

// PATH DATA OF WHERE TO STORE FILES
const { PATHS } = require("../Configs/constants");

//GET AWS CONFIG
const credentials = {
    accessKeyId: process.env.AWS_ACCESS_KEY,
    secretAccessKey: process.env.AWS_SECRET_KEY,
    Bucket: process.env.AWS_BUCKET_NAME,
    region: process.env.AWS_REGION,
    signatureVersion: 'v4'
}

//S3 CONFIG
const s3 = new AWS.S3(credentials)

// SAVE THIS TEMP FOLDER PATH ONE TIME
let tempPath = {
    original: path.join(__dirname, "../Assets/Images" + PATHS.TEMP + PATHS.ORIGINAL + "/"),
    thumb: path.join(__dirname, "../Assets/Images" + PATHS.TEMP + PATHS.THUMB + "/"),
}
tempPath[PATHS.ORIGINAL] = tempPath.original
tempPath[PATHS.THUMB] = tempPath.thumb;


module.exports = class FileManager {


    //Multer setup
    static upload() {
        const storage = multer.diskStorage({
            destination: function (req, file, cb) {
                cb(null, tempPath.original)
            },
            filename: function (req, file, cb) {
                let fileName = FileManager.getNameFormFileName(file.originalname)
                if (!req.body[file.fieldname]) {
                    req.body[file.fieldname] = []
                }
                req.body[file.fieldname].push(fileName)
                cb(null, fileName)
            }
        })

        return multer({ storage })
    }


    static getNameFormFileName(fileName) {
        return fileName.split('.')[0].replace(/[^A-Z0-9]/ig, "_") + '_' + Date.now() + '_' + Math.floor(Math.random() * 999) + 99 + path.extname(fileName);
    }


    //Get filename from url
    static getFileNameFromUrl(url) {
        return url.split('/').pop().split('#').shift().split('?').shift()
    }

    //Get url from cloud
    static getUrl(pathName, fileName, isPublic = isBucketPublic) {
        const bucket = process.env.AWS_BUCKET_NAME
        const key = (process.env.AWS_BUCKET_FOLDER || "") + pathName + "/" + fileName

        if (isPublic) {
            return `https://${bucket}.s3.amazonaws.com/${key}`
        }
        else {
            return s3.getSignedUrl('getObject', {
                Bucket: bucket,
                Key: key,
                Expires: (5 * 24 * 60 * 60), //5 day
                ResponseContentDisposition: "inline"
            })
        }
    }

    //Upload file or files to cloud
    static async uploadToCloud(files, primary, secondary) {
        if (!files) return
        const arrayFiles = (Array.isArray(files)) ? files : [files]

        return await Promise.all(arrayFiles.map(file => {
            //Upload single file to aws
            let localFile = tempPath[secondary ?? PATHS.ORIGINAL] + file
            let cloudPath = primary + (secondary ?? "")

            return new Promise((resolve, reject) => {
                let params = {
                    Bucket: process.env.AWS_BUCKET_NAME,
                    Body: fs.createReadStream(localFile),
                    Key: (process.env.AWS_BUCKET_FOLDER || "") + cloudPath + "/" + file,
                    ContentType: path.extname(file),
                }

                if (isBucketPublic)
                    params.ACL = 'public-read'

                s3.upload(params, function (err, data) {
                    if (err) reject(err);
                    else {
                        // If Success then Remove file from local
                        // console.log(data)
                        fs.unlink(localFile, function (err) {
                            if (err) console.log(err)
                        })
                        resolve(data)
                    }
                })
            })
        }));
    }

    //Delete file or files from cloud
    static async delete(files, primary, secondary) {
        if (!files) return
        const arrayFileNames = (Array.isArray(files)) ? files : [files]

        return await Promise.all(arrayFileNames.map(fileName => {
            let params = {
                Bucket: process.env.AWS_BUCKET_NAME,
                Key: (process.env.AWS_BUCKET_FOLDER || "") + primary + secondary + "/" + fileName
            }

            return new Promise((resolve, reject) => {
                s3.deleteObject(params, function (err, data) {
                    if (err) reject(err)
                    else resolve(data)
                })
            })
        }))
    }

    //Delete file or files from cloud
    static async deleteUrl(url) {
        if (!url) return

        let params = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: url
        }

        return new Promise((resolve, reject) => {
            s3.deleteObject(params, function (err, data) {
                if (err) reject(err)
                else resolve(data)
            })
        })
    }


    static async generateThumbAndUploadToCloud(fileNames, primary) {
        if (!fileNames) return
        const arrayFileNames = (Array.isArray(fileNames)) ? fileNames : [fileNames]

        const arrayThumbData = await Promise.all(arrayFileNames.map(fileName => {
            return FileManager.generateImageThumb(fileName)
        }))

        const arrayThumbFileNames = arrayThumbData.map(data => {
            return data.fileName
        })

        // UPLOAD TO CLOUD
        await Promise.all([
            FileManager.uploadToCloud(arrayFileNames, primary, PATHS.ORIGINAL),
            FileManager.uploadToCloud(arrayThumbFileNames, primary, PATHS.THUMB),
        ])

        return arrayThumbData
    }


    static async generateImageThumb(file) {
        return await FileManager.compressImage(file, tempPath.original, tempPath.thumb)
    }

    static async compressImage(fileName, primary, secondary) {
        const srcImage = Sharp(primary + fileName, { failOnError: false })

        const metadata = await srcImage.metadata()
        const data = {
            fileName: fileName,
            ratio: metadata.width / metadata.height,
            height: metadata.height,
            width: metadata.width,
        }

        const buffer = await srcImage.resize(undefined, IMAGE_RESIZE_RESOLUTION).rotate().withMetadata().toBuffer()
        await Sharp(buffer).toFile(secondary + fileName)

        return data
    }

    static async changeFileLocation(currentPaths, fileName) {

        const bucketName = process.env.AWS_BUCKET_NAME;
        currentPaths = (Array.isArray(currentPaths)) ? currentPaths : [currentPaths]
        const movingFilesToTrash = [];
        try {
            for(let currentPath of currentPaths){
                const oldKey = (process.env.AWS_BUCKET_FOLDER || "") + currentPath + "/" + fileName;
                const newKey = (process.env.AWS_BUCKET_FOLDER || "") + PATHS.TRASH_FOLDER_BUCKET + "/" + fileName;
                
                const moveOperation = Promise.all([
                    s3.copyObject({
                        Bucket: bucketName,
                        CopySource: `${bucketName}/${oldKey}`,
                        Key: newKey,
                      }).promise(),
                    s3.deleteObject({
                        Bucket: bucketName,
                        Key: oldKey
                    }).promise()
                ]);

                movingFilesToTrash.push(moveOperation);

            }

            return movingFilesToFolder;
          } catch (error) {
            console.error('Error changing file location:', error);
          }
    }

}