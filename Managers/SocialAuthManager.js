//Google
const { OAuth2Client } = require("google-auth-library");
const jwt = require('jsonwebtoken');
const jwksClient = require('jwks-rsa');

//Facebook
const axios = require("axios");
const queryString = require("querystring");

module.exports = class SocialAuthManager {
  googleAuthVerification = async (token) => {
    try {
      const audience = [
        process.env.GOOGLE_WEB_CLIENT_ID,
        process.env.GOOGLE_IOS_CLIENT_ID,
        process.env.GOOGLE_ANDROID_CLIENT_ID_RELEASE,
        process.env.GOOGLE_ANDROID_CLIENT_ID_DEBUG,
      ];
      const client = new OAuth2Client(audience);
      const ticket = await client.verifyIdToken({
        idToken: token,
        audience,
      });
      const payload = ticket.getPayload();

      return {
        id: payload.sub,
        email: payload.email,
        firstName: payload.given_name,
        lastName: payload.family_name,
        picture: payload.picture,
      };
    } catch (err) {
      console.log(err);
      return undefined;
    }
  };

  facebookAuthVerification = async (token) => {
    try {
      const queryParams = queryString.stringify({
        access_token: token,
        fields: [
          "id",
          "first_name",
          "last_name",
          "email",
          "picture.type(large)",
        ].join(","),
      });
      let fbResponse = await axios.get(
        process.env.FACEBOOK_AUTH_VERIFY_URL + queryParams
      );
      return {
        id: fbResponse.data.id,
        email: fbResponse.data.email,
        firstName: fbResponse.data.first_name,
        lastName: fbResponse.data.last_name,
        picture: fbResponse.data.picture
          ? fbResponse.data.picture.data.url
          : undefined,
      };
    } catch (err) {
      return undefined; 
    }
  };

  appleAuthVerification = async (token) => {
    try {
        const client = jwksClient({
          jwksUri: process.env.APPLE_AUTH_VERIFY_URL,
          timeout: 30000 
        });
    
        const decodedToken = jwt.decode(token, { complete: true });
        const kid = decodedToken.header.kid;
    
        const key = await client.getSigningKey(kid);
        const publicKey = key.getPublicKey();
    
        const payload = jwt.verify(token, publicKey, {
          algorithms: ['RS256'],
          audience: process.env.APPLE_ID_TOKEN,
          issuer: 'https://appleid.apple.com',
        });
    
        return {
          id: payload.sub,
          email: payload.email,
          firstName: payload?.firstName || '',
          lastName: payload?.lastName || '',
          picture: undefined 
        };
      } catch (err) {
        console.log(err);
        return undefined;
      }
  };
};
