const nodemailer = require("nodemailer");
const jwt = require('jsonwebtoken');
const path = require('path');

const { EMAIL_TYPE_SLUG } = require("../Configs/constants");

const TemplateModel = new (require("../Models/TemplateModel"))();

module.exports = class {

    constructor(senderEmail = null) {
        if (senderEmail) {
            this.fromEmail = senderEmail;
        }
        else {
            this.setSenderEmail()
        }
    }

    transporter = nodemailer.createTransport({
        host: process.env.AWS_SMTP_HOST, 
        port: 465, 
        secure: true, 
        auth: {
            user: process.env.AWS_SMTP_USERNAME, 
            pass: process.env.AWS_SMTP_PASSWORD 
        }
    });

    setSenderEmail = async () => {
        this.fromEmail = process.env.EMAIL_ADDRESS; 
    };

    convertedEmail = (html) => {
        return `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f4f4f4; font-family: Arial, sans-serif;">
            <div style="text-align: center;">
                <img src="cid:logo@png" alt="${process.env.PROJECT_NAME}" width="auto" height="50" style="display: block; margin: 0 auto;">
            </div>
            <div style="background: #ffffff; margin-top: 20px; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);">
                ${html}
            </div>
            <p style="color: #777; font-size: 12px; text-align: center; margin-top: 20px;">
                Best regards, <br/>
                <strong>${process.env.PROJECT_NAME}</strong>
            </p>
        </div>
        `
    }

    sendMail = async ({ receiverMail, subject, senderEmail, text, html, attachments }) => {
        try {
            const mailOption = {
                from: senderEmail ?? this.fromEmail,
                to: receiverMail,
                subject,
                text: text ?? "",
                html: this.convertedEmail(html),
                attachments: [
                    {
                        filename: 'logo.png', // PNG file to embed
                        path: path.resolve(__dirname, 'logo.png'),  // Path to the PNG file
                        cid: 'logo@png',     // Set a unique Content-ID
                    },
                    ...(attachments ?? []),
                ]
            };
            
            return await this.transporter.sendMail(mailOption)
        }
        catch (err) {
            console.log(err.message);
        }
    }

    forgotPasswordMail = async (user) => {
        try {
            const token = jwt.sign(
                { id: user._id },
                process.env.JWT_SECRET + user.password,
                { expiresIn: '15m' }
            );

            const resetPasswordUrl = `${process.env.WEB_URL}/reset-password?token=${token}`;

            const { title, content } = await TemplateModel.getUpdatedTemplate(
                EMAIL_TYPE_SLUG.FORGOT_PASSWORD,
                {
                    URL: resetPasswordUrl,
                    APP_NAME: process.env.PROJECT_NAME
                }
            );

            await this.sendMail({
                receiverMail: user.email,
                subject: title,
                html: content
            });
        } catch (error) {
            console.error("Error sending forgot password email:", error);
            throw error;
        }
    };


    userVerificationDeniedMail = async (user) => {
        const {
            title,
            content
        } = await TemplateModel.getUpdatedTemplate(
            EMAIL_TYPE_SLUG.USER_KYC_VERIFICATION_DENIED,
            {
                APP_NAME: process.env.PROJECT_NAME,
                REASON: user.verification.reason ?? ""
            }
        )

        this.sendMail({
            receiverMail: user.email,
            subject: title,
            html: content
        })
    };

    userWelcomeMail = async (user) => {
        const {
            title,
            content
        } = await TemplateModel.getUpdatedTemplate(
            EMAIL_TYPE_SLUG.USER_WELCOME,
            {
                APP_NAME: process.env.PROJECT_NAME,
            }
        )

        this.sendMail({
            receiverMail: user.email,
            subject: title,
            html: content
        })
    };

    userResubmitVerificationMail = async (user) => {
        const {
            title,
            content
        } = await TemplateModel.getUpdatedTemplate(
            EMAIL_TYPE_SLUG.RESUBMIT_VERIFICATION,
            {
                APP_NAME: process.env.PROJECT_NAME,
                REASON: user.verification.reason ?? ""
            }
        )

        this.sendMail({
            receiverMail: user.email,
            subject: title,
            html: content
        })
    };

    temporaryPasswordMail = async (user, password) => {
        const {
            title,
            content
        } = await TemplateModel.getUpdatedTemplate(
            EMAIL_TYPE_SLUG.USER_TEMP_PASSWORD,
            {
                APP_NAME: process.env.PROJECT_NAME,
                email: user.email,
                password
            }
        )

        return this.sendMail({
            receiverMail: user.email,
            subject: title,
            html: content
        })

    }

    sendPdfWithMail = async (pdfBuffer) => {
        const {
            title,
            content
        } = await TemplateModel.getUpdatedTemplate(
            EMAIL_TYPE_SLUG.QUARTERLY_REPORT,
            {
                APP_NAME: process.env.PROJECT_NAME,
            }
        )

        return this.sendMail({
            receiverMail: process.env.PDF_RECEIVER_EMAIL,
            subject: title,
            html: content,
            attachments: [
                {
                    filename: "Quarterly_Report.pdf",
                    content: pdfBuffer,
                    contentType: "application/pdf",
                },
            ],
        })
    }

    userPaymentStatusMail = async (email, TransactionId, status) => {
        const {
            title,
            content
        } = await TemplateModel.getUpdatedTemplate(
            EMAIL_TYPE_SLUG.USER_PAYMENT_STATUS,
            {
                APP_NAME: process.env.PROJECT_NAME,
                ID: TransactionId,
                STATUS: status
            }
        )
        this.sendMail({
            receiverMail: email,
            subject: title,
            html: content
        })
    };

    userRoleUpdate = async (email) => {
        const {
            title,
            content
        } = await TemplateModel.getUpdatedTemplate(
            EMAIL_TYPE_SLUG.USER_ROLE_UPDATE,
            {
                APP_NAME: process.env.PROJECT_NAME,
            }
        )
        this.sendMail({
            receiverMail: email,
            subject: title,
            html: content
        })
    };

}
