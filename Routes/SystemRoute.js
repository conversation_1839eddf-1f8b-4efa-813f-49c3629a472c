const express = require("express");
const router = express.Router();

const SystemController = new (require("../Controllers/SystemController"))();
const SystemValidator = require("../Middlewares/Validators/SystemValidator");

const Authentication = require("../Middlewares/authentication")

router.route("/value")
    .get(
        SystemValidator.findSystemValue,
        Authentication.user,
        SystemController.findSystemValue
    )
    .put(
        SystemValidator.updateSystemValue,
        Authentication.user,
        SystemController.updateSystemValue
    )

router.route("/pages")
    .get(
        SystemValidator.listPages,
        Authentication.user,
        SystemController.listPages
    )
    .post(
        SystemValidator.getPageDetail,
        Authentication.blank,
        SystemController.getPageDetail
    )
    .put(
        SystemValidator.updatePage,
        Authentication.user,
        SystemController.updatePage
    )


router.route("/templates")
    .get(
        SystemValidator.listTemplates,
        Authentication.user,
        SystemController.listTemplates
    )
    .post(
        SystemValidator.getTemplateDetail,
        Authentication.blank,
        SystemController.getTemplateDetail
    )
    .put(
        SystemValidator.updateTemplate,
        Authentication.user,
        SystemController.updateTemplate
    )


module.exports = router;
