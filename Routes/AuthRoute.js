const express = require("express");

const AuthValidator = require("../Middlewares/Validators/AuthValidator");
const Authentication = require("../Middlewares/authentication")
const AuthController = new (require("../Controllers/AuthController"))()

const router = express.Router();

router.route("/signIn")
    .post(
        AuthValidator.signIn,
        Authentication.blank,
        AuthController.signIn
    )

router.route("/socialSignIn")
    .post(
        AuthValidator.socialSignIn,
        Authentication.blank,
        AuthController.socialSignIn
    )

router.route("/signUp")
    .post(
        AuthValidator.signUp,
        Authentication.blank,
        AuthController.signUp
    )

router.route("/forgotPassword")
    .post(
        AuthValidator.forgotPassword,
        Authentication.blank,
        AuthController.forgotPassword
    )
    .get(
        AuthValidator.verifyForgotPassword,
        Authentication.blank,
        AuthController.verifyForgotPassword
    )
    .put(
        AuthValidator.resetPassword,
        Authentication.blank,
        AuthController.resetPassword
    )

module.exports = router;
