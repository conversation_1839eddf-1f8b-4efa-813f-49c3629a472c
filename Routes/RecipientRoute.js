const express = require("express");
const router = express.Router();

const RecipientValidator = require("../Middlewares/Validators/RecipientValidator");
const RecipientController = new (require("../Controllers/RecipientController"))()

const Authentication = require("../Middlewares/authentication")

router.route("/")
    .post(
        RecipientValidator.createRecipient,
        Authentication.user,
        RecipientController.createRecipient
    )
    .get(
        RecipientValidator.listRecipient,
        Authentication.user,
        RecipientController.listRecipient
    )
    .put(
        RecipientValidator.updateRecipient,
        Authentication.user,
        RecipientController.updateRecipient
    )
    .delete(
        RecipientValidator.deleteRecipient,
        Authentication.user,
        RecipientController.deleteRecipient
    );

module.exports = router;
