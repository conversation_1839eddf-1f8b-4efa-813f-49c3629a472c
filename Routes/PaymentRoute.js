const express = require("express");
const router = express.Router();

const PaymentController = new (require("../Controllers/PaymentController"))()
const PaymentValidator = require("../Middlewares/Validators/PaymentValidator");

const Authentication = require("../Middlewares/authentication")

router.route("/createPayment")
    .post(
        PaymentValidator.createPayment,
        Authentication.user,
        PaymentController.createPayment
    );

router.route("/webhookEvent")
    .post(
        Authentication.blank,
        PaymentController.paymentWebhookEvent
    )

router.route("/country")
    .get(
        Authentication.blank,
        PaymentController.listCountries
    )

router.route("/city")
    .get(
        PaymentValidator.countryQueryValidator,
        Authentication.blank,
        PaymentController.listCity
    )

router.route("/state/check")
    .post(
        PaymentValidator.validStateCheckValidator,
        Authentication.user,
        PaymentController.validStateCheck
    )

router.route("/state")
    .get(
        Authentication.blank,
        PaymentController.listState
    )

router.route("/state/toggle-status")
    .put(
        PaymentValidator.stateIdValidator,
        Authentication.blank,
        PaymentController.toggleStateStatus
    )

router.route("/service")
    .get(
        PaymentValidator.listServices,
        Authentication.blank,
        PaymentController.listServices
    )

router.route("/checkRecipient")
    .get(
        PaymentValidator.checkRecipient,
        Authentication.user,
        PaymentController.checkRecipient
    )

router.route("/charges")
    .get(
        Authentication.blank,
        PaymentController.getCharges
    )

router.route("/exchange-rates")
    .post(
        PaymentValidator.exchangeRateValidator,
        Authentication.blank,
        PaymentController.getExchangeRate
    )

router.route("/pricing")
    .get(
        PaymentValidator.countryQueryValidator,
        Authentication.blank,
        PaymentController.listPricing
    )

module.exports = router;
