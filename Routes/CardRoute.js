const express = require("express");
const router = express.Router();

const CardValidator = require("../Middlewares/Validators/CardValidator");
const CardController = new (require("../Controllers/CardController"))()

const Authentication = require("../Middlewares/authentication")

router.route("/")
    .post(
        CardValidator.createCard,
        Authentication.user,
        CardController.createCard
    )
    .get(
        CardValidator.listCard,
        Authentication.user,
        CardController.listCard
    )
    .delete(
        CardValidator.deleteCard,
        Authentication.user,
        CardController.deleteCard
    );

module.exports = router;
