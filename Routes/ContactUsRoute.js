const express = require("express");
const Authentication = require("../Middlewares/authentication");
const contactUsValidator = require("../Middlewares/Validators/ContactUsValidator");
const ContactUsController =
  new (require("../Controllers/ContactUsController"))();
const router = express.Router();

router
  .route("/")
  .post(
    contactUsValidator.validateContactUs,
    Authentication.blank,
    ContactUsController.addContactUs
  );

module.exports = router;
