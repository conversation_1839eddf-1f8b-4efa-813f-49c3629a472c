const express = require("express");
const router = express.Router();

const FaqValidator = require("../Middlewares/Validators/FaqValidator");
const Authentication = require("../Middlewares/authentication")
const FaqController = new (require("../Controllers/FaqController"))()

router.route("/")
    .get(
        FaqValidator.listFaqs,
        Authentication.blank,
        FaqController.listFaqs
    )

module.exports = router;
