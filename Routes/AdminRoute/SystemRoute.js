const express = require("express");
const router = express.Router();

const SystemController = new (require("../../Controllers/SystemController"))();
const SystemValidator = require("../../Middlewares/Validators/SystemValidator");

const Authentication = require("../../Middlewares/authentication")

router.route("/pages")
    .get(
        SystemValidator.listPages,
        Authentication.admin,
        SystemController.listPages
    )
    .post(
        SystemValidator.getPageDetail,
        Authentication.blank,
        SystemController.getPageDetail
    )
    .put(
        SystemValidator.updatePage,
        Authentication.admin,
        SystemController.updatePage
    )

router.route("/templates")
    .get(
        SystemValidator.listTemplates,
        Authentication.admin,
        SystemController.listTemplates
    )
    .post(
        SystemValidator.getTemplateDetail,
        Authentication.blank,
        SystemController.getTemplateDetail
    )
    .put(
        SystemValidator.updateTemplate,
        Authentication.admin,
        SystemController.updateTemplate
    )


module.exports = router;
