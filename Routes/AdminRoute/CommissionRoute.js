const express = require("express");
const commissionController =
  new (require("../../Controllers/CommissionController"))();
const Authentication = require("../../Middlewares/authentication");
const { MODULE_NAME_SLUG, ACTIONS } = require("../../Configs/constants");
const { commissionValidator } = require("../../Middlewares/Validators/ComissionValidator");

const router = express.Router();

router
  .route("/")
  .patch(
    commissionValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.commissionManagement,
        ACTIONS.EDIT
      ),
    commissionController.updateCommission
  )
  .get(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.commissionManagement,
        ACTIONS.VIEW
      ),
    commissionController.getList
  );

module.exports = router;
