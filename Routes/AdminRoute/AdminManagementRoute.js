const express = require("express");
const {
  imageFileValidator,
} = require("../../Middlewares/Validators/FileValidators");
const adminController = new (require("../../Controllers/AdminController"))();
const upload = require("../../Managers/FileManager").upload;

const Authentication = require("../../Middlewares/authentication");
const { MODULE_NAME_SLUG, ACTIONS } = require("../../Configs/constants");

const router = express.Router();

router
  .route("/sub-admin")
  .post(
    upload().fields([{ name: "profilePicture", maxCount: 1 }]),
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.adminManagement,
        ACTIONS.CREATE
      ),
    imageFileValidator,
    adminController.addSubAdmin
  )
  .delete(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.adminManagement,
        ACTIONS.DELETE
      ),
    adminController.deactivateAdmin
  )
  .get(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.adminManagement,
        ACTIONS.VIEW
      ),
    adminController.listSubAdmin
  );

router
  .route("/sub-admin/:userId")
  .patch(
    upload().fields([{ name: "profilePicture", maxCount: 1 }]),
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.adminManagement,
        ACTIONS.EDIT
      ),
    imageFileValidator,
    adminController.editSubAdmin
  );

module.exports = router;
