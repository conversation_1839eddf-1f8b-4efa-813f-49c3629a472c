const express = require("express");
const {
  csvFileValidator,
  imageFileValidator,
} = require("../../Middlewares/Validators/FileValidators");
const adminController = new (require("../../Controllers/AdminController"))();
const UserController = new (require("../../Controllers/UserController"))();
const upload = require("../../Managers/FileManager").upload;

const Authentication = require("../../Middlewares/authentication");
const { MODULE_NAME_SLUG, ACTIONS } = require("../../Configs/constants");
const {
  userListValidator,
  addUserFromAdmin,
  bodyMongoIdValidator,
  paramsMongoIdValidator,
  passwordBodyValidator
} = require("../../Middlewares/Validators/UserManagementValidator");
const {
  basicFilterValidator,
} = require("../../Middlewares/Validators/CommonValidator");

const router = express.Router();

router
  .route("/")
  .get(
    userListValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.VIEW
      ),
    adminController.listUsers
  )
  .post(
    upload().fields([
      { name: "profilePicture", maxCount: 1 },
      { name: "files", maxCount: 2 },
    ]),
    addUserFromAdmin,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.CREATE
      ),
    imageFileValidator,
    adminController.addUser
  )
  .delete(
    bodyMongoIdValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.DELETE
      ),
    adminController.deactivateUser
  );

router
  .route("/resetPassword")
  .put(
    bodyMongoIdValidator,
    passwordBodyValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.EDIT
      ),
    adminController.resetPassword
  )

router
  .route("/import")
  .post(
    upload().single("file"),
    csvFileValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.CREATE
      ),
    adminController.importUser
  );

router
  .route("/export")
  .get(
    userListValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.VIEW
      ),
    adminController.exportCsv
  );

router
  .route("/docs")
  .post(
    bodyMongoIdValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.VIEW
      ),
    adminController.downloadDocument
  );

router
  .route("/sources")
  .get(
    Authentication.admin,
    adminController.getUserSource
  );

router
  .route("/logs")
  .get(
    basicFilterValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.logManagement,
        ACTIONS.VIEW
      ),
    adminController.findActionLogLists
  );

router
  .route("/logs/entity")
  .get(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.logManagement,
        ACTIONS.VIEW
      ),
    adminController.findActionLogEntityLists
  );

router.get(
  "/download-csv",
  Authentication.admin,
  (req, res, next) =>
    Authentication.hasModuleAccess(
      req,
      res,
      next,
      MODULE_NAME_SLUG.userManagement,
      ACTIONS.CREATE
    ),
  adminController.demoCsvDownload
);
  
router
  .route("/verification/media")
  .post(Authentication.admin, UserController.getMedia);

router
  .route("/:userId")
  .put(
    paramsMongoIdValidator,
    upload().fields([
      { name: "profilePicture", maxCount: 1 },
      { name: "files", maxCount: 2 },
    ]),
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.EDIT
      ),
    imageFileValidator,
    adminController.editUser
  )
  .get(
    paramsMongoIdValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.userManagement,
        ACTIONS.VIEW
      ),
    adminController.getUserInfoById
  );


module.exports = router;
