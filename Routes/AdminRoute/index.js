const express = require("express");

const router = express.Router();

router.use("/usermanagement", require("./UserManagementRoute"));
router.use("/commission", require("./CommissionRoute"));
router.use("/transactions", require("./TransactionRoute"));
router.use("/permissions", require("./PermissionRoute"));
router.use("/management", require("./AdminManagementRoute"));
router.use("/roles", require("./RoleRoute"));
router.use("/system", require("./SystemRoute"));
router.use("/faqs", require("./FaqManagementRoute"));
router.use("/dashboard", require("./DashboardRoute"));

module.exports = router;
