const express = require("express");
const Authentication = require("../../Middlewares/authentication"); 
const DashboardController = new(require("../../Controllers/DashboardController"))();

const router = express.Router();

router.route("/").get(Authentication.admin, DashboardController.getInsights);

router.route("/pdf").get(Authentication.admin, DashboardController.getInsightsForPDF);

module.exports = router;