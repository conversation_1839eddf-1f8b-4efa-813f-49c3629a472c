const express = require("express");
const TransactionController =
  new (require("../../Controllers/TransactionController"))();
const { MODULE_NAME_SLUG, ACTIONS } = require("../../Configs/constants");
const Authentication = require("../../Middlewares/authentication");
const { transactionListingValidator, transactionLimitValidator } = require("../../Middlewares/Validators/TransactionValidator");

const router = express.Router();

router
  .route("/")
  .get(
    transactionListingValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.transaction,
        ACTIONS.VIEW
      ),
    TransactionController.listTransaction
  );

router
  .route("/export")
  .get(
    transactionListingValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.transaction,
        ACTIONS.VIEW
      ),
    TransactionController.exportTransaction
  );

router
  .route("/payout-method")
  .get(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.transaction,
        ACTIONS.VIEW
      ),
    TransactionController.distinctPayoutMethod
  );

router
  .route("/count")
  .get(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.transaction,
        ACTIONS.VIEW
      ),
    TransactionController.getTransactionNumbers
  );

router
  .route("/transaction-limit")
  .get(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.transactionLimit,
        ACTIONS.VIEW
      ),
    TransactionController.getTransactionLimit
  )
  .put(
    transactionLimitValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.transactionLimit,
        ACTIONS.VIEW
      ),
    TransactionController.updateTransactionLimit
  );

module.exports = router;
