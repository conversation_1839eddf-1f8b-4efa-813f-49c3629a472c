const express = require("express");
const RolesController = new (require("../../Controllers/RoleController"))();
const { MODULE_NAME_SLUG, ACTIONS } = require("../../Configs/constants");
const Authentication = require("../../Middlewares/authentication");
const {
  newRoleValidator,
} = require("../../Middlewares/Validators/AddNewRoleValidator");
const { deleteFaqs } = require("../../Middlewares/Validators/FaqValidator");

const router = express.Router();

router
  .route("/")
  .get(Authentication.admin, RolesController.getAllRoles)
  .post(
    newRoleValidator,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.authorization,
        ACTIONS.CREATE
      ),
    RolesController.addRole
  );

router
  .route("/:id")
  .delete(
    deleteFaqs,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.authorization,
        ACTIONS.DELETE
      ),
    RolesController.deleteRole
  );

module.exports = router;
