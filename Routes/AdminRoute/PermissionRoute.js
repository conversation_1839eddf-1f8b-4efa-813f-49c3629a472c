const express = require("express");
const { MODULE_NAME_SLUG, ACTIONS } = require("../../Configs/constants");
const Authentication = require("../../Middlewares/authentication");
const PermissionController =
  new (require("../../Controllers/PermissionController"))();

const router = express.Router();

router
  .route("/")
  .patch(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.authorization,
        ACTIONS.EDIT
      ),
    PermissionController.updateRolePermission
  )
  .get(
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.authorization,
        ACTIONS.VIEW
      ),
    PermissionController.getPermissionByRoles
  );

router
  .route("/sub-admin")
  .get(
    Authentication.admin,
    PermissionController.getPermissionByRoles
  );

module.exports = router;
