const express = require("express");
const Authentication = require("../../Middlewares/authentication");
const FaqController = new (require("../../Controllers/FaqController"))();
const FaqValidator = require("../../Middlewares/Validators/FaqValidator");
const { MODULE_NAME_SLUG, ACTIONS } = require("../../Configs/constants");

const router = express.Router();

router
  .route("/")
  .get(
    FaqValidator.listFaqs,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.faqManagement,
        ACTIONS.VIEW
      ),
    FaqController.listFaqs
  )
  .post(
    FaqValidator.addFaqs,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.faqManagement,
        ACTIONS.CREATE
      ),
    FaqController.addFaqs
  );

router
  .route("/:id")
  .put(
    FaqValidator.updateFaqs,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.faqManagement,
        ACTIONS.EDIT
      ),
    FaqController.updateFaqs
  )
  .delete(
    FaqValidator.deleteFaqs,
    Authentication.admin,
    (req, res, next) =>
      Authentication.hasModuleAccess(
        req,
        res,
        next,
        MODULE_NAME_SLUG.faqManagement,
        ACTIONS.DELETE
      ),
    FaqController.deleteFaqs
  );

module.exports = router;
