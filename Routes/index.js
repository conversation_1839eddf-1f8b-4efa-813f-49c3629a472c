const express = require('express');
const router = express.Router();

module.exports = (app) => {
    router.get("/", (req, res) => {
        res.status(200).send("Welcome to " + process.env.PROJECT_NAME);
    });

    router.use("/auth", require("./AuthRoute"));
    router.use("/faq", require("./FaqRoute"));
    router.use("/payment", require("./PaymentRoute"));
    router.use("/recipient", require("./RecipientRoute"));
    router.use("/system", require("./SystemRoute"));
    router.use("/card", require("./CardRoute"));
    router.use("/user", require("./UserRoute"));
    router.use("/admin", require("./AdminRoute"));
    router.use("/contactus", require("./ContactUsRoute"));
    router.use("/transaction", require("./TransactionRoute"));

    app.use("/api", router);
};
