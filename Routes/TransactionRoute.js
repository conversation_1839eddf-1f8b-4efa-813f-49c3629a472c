const express = require("express");
const router = express.Router();

const PaymentController = new (require("../Controllers/PaymentController"))()
const TransactionController = new (require("../Controllers/TransactionController"))()
const PaymentValidator = require("../Middlewares/Validators/PaymentValidator");
const TransactionValidator = require("../Middlewares/Validators/TransactionValidator");

const Authentication = require("../Middlewares/authentication")

router
  .route("/")
  .get(
    TransactionValidator.transactionValidator,
    Authentication.user,
    TransactionController.listTransaction
  );

router
  .route("/track")
  .post(
    PaymentValidator.trackTransaction,
    Authentication.blank,
    PaymentController.trackTransaction
  );

router
  .route("/transaction-limit")
  .get(
    Authentication.blank,
    TransactionController.getTransactionLimit
  )

module.exports = router;
