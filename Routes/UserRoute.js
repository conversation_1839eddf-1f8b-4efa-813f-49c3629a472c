const express = require("express");
const router = express.Router();

const UserController = new (require("../Controllers/UserController"))();
const UserValidator = require("../Middlewares/Validators/UserValidator");

const Authentication = require("../Middlewares/authentication")
const upload = require('../Managers/FileManager').upload

router.route("/profile")
    .get(
        UserValidator.getProfile,
        Authentication.all,
        UserController.getProfile
    )
    .put(
        upload().any(),
        UserValidator.updateProfile,
        Authentication.all,
        UserController.updateProfile
    )
    .delete(
        Authentication.all,
        UserController.deleteProfile
    )

router.route("/verification")
    .get(
        Authentication.user,
        UserController.createVerification
    )

router.route("/verification/media/session")
    .get(
        Authentication.user,
        UserController.getMediaSession
    )

router.route("/verification/media")
    .post(
        Authentication.user,
        UserController.getMedia
    )

router.route("/verification/webhookEvent")
    .post(
        UserController.verificationWebhookEvent
    )

router.route("/verification/webhookDecision")
    .post(
        UserController.verificationWebhookDecision
    )

router.route("/authentication")
    .put(
        UserValidator.changePassword,
        Authentication.all,
        UserController.changePassword
    )
    .delete(
        Authentication.all,
        UserController.signOut
    )

module.exports = router;
