const cron = require("node-cron");
const moment = require("moment");
const nodemailer = require("nodemailer");
const { PDFDocument, StandardFonts, rgb } = require("pdf-lib");
const TransactionModel = require("../Models/TransactionsModel");
const MegaTechModel = require("../Models/MegaTechModel");
const EmailManager = new (require("../Managers/EmailManager"))();


async function generatePDF() {
    try {
        const startDate = moment().subtract(3, "months").format("YYYY-MM-DD");
        const endDate = moment().subtract(1, "days").format("YYYY-MM-DD");

        const countryList = await MegaTechModel.getDestinationCountries();
        const usDetails = countryList.find((country) => country.CountryCode === "US");
        const usCountryId = parseInt(usDetails.countryId);

        const queries = [
            TransactionModel.getTransactionInsightForPDF(startDate, endDate, {
                $match: { destinationCountryId: { $ne: usCountryId } },
            }),
            TransactionModel.getTransactionInsightForPDF(startDate, endDate, {
                $match: { destinationCountryId: usCountryId },
            }),
            TransactionModel.getTransactionInsightForPDF(startDate, endDate),
        ];

        const [usToForeign, usToUs, total] = await Promise.all(queries);

        // Create PDF
        const pdfDoc = await PDFDocument.create();
        const page = pdfDoc.addPage([500, 400]);
        const { width, height } = page.getSize();
        const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

        let y = height - 50;
        const fontSize = 12;
        const paddingLeft = 20;

        page.drawText("Transaction Report", {
            x: width / 2 - 100,
            y,
            size: 20,
            font,
            color: rgb(0, 0, 0),
        });

        y -= 40;

        page.drawText(`Submitted ${moment().format("MM/DD/YYYY")} by Admin`, {
            x: paddingLeft,
            y,
            size: 12,
            font,
            color: rgb(0, 0, 0),
        });

        y -= 20;

        // Table Data
        const tableData = [
            { code: "TA10", label: "Total # of transactions (US to US)", value: String(usToUs[0]?.totalTransactionCount || 0) },
            { code: "TA20", label: "Total $ amount (US to US)", value: `$ ${String(usToUs[0]?.totalAmountSum || 0)}` },
            { code: "TA30", label: "Total # of transactions (US to Foreign)", value: String(usToForeign[0]?.totalTransactionCount || 0) },
            { code: "TA40", label: "Total $ amount (US to Foreign)", value: `$ ${String(usToForeign[0]?.totalAmountSum || 0)}` },
            { code: "TA50", label: "Total # of All Transactions", value: String(total[0]?.totalTransactionCount || 0) },
            { code: "TA60", label: "Total $ Amount of All Transactions", value: `$ ${String(total[0]?.totalAmountSum || 0)}` },
        ];

        tableData.forEach((row, index) => {
            page.drawText(`${row.code}: ${row.label} - ${row.value}`, {
                x: paddingLeft,
                y: y - index * 20,
                size: fontSize,
                font,
                color: rgb(0, 0, 0),
            });
        });
        return await pdfDoc.save();
    } catch (err) {
        console.error("Error generating PDF:", err);
        return null;
    }
}

// Schedule cron job to run quarterly (every 1st day of Jan, Apr, Jul, Oct at 10 AM)
cron.schedule("0 0 10 1 1,4,7,10 *", async function () {
    console.log("Running Quarterly Report Cron Job...");
    try {
        const pdfBuffer = await generatePDF();
        if (pdfBuffer) {
            await EmailManager.sendPdfWithMail(pdfBuffer);
        } else {
            console.error("Failed to generate PDF.");
        }
    } catch (error) {
        console.error("Error in cron job:", error);
    }
}, {
    timezone: "America/New_York"
});

