const mongoose = require("mongoose");

const { ENVIRONMENTS } = require("./constants");

const dbName = process.env.DB_NAME;
const region = process.env.REGION || 'default';

// Apply timestamp plugin to all schemas
mongoose.plugin((schema) => {
  schema.set("timestamps", true); // Enable timestamps
  schema.set("versionKey", false); // Disable __v field
});

// Regional database connection management
class DatabaseManager {
  constructor() {
    this.connections = new Map();
    this.defaultConnection = null;
  }

  async connectToRegion(regionCode = region, mongoUrl = process.env.MONGODB_URL, databaseName = dbName) {
    const connectionKey = `${regionCode}_${databaseName}`;

    if (this.connections.has(connectionKey)) {
      return this.connections.get(connectionKey);
    }

    try {
      const connection = await mongoose.createConnection(mongoUrl, {
        dbName: databaseName,
      });

      this.connections.set(connectionKey, connection);

      console.log(`${databaseName} database connected successfully for region: ${regionCode} :)`);

      // Set as default if it's the first connection or matches current region
      if (!this.defaultConnection || regionCode === region) {
        this.defaultConnection = connection;
      }

      return connection;
    } catch (err) {
      console.error(`Failed to connect to database for region ${regionCode}:`, err);
      throw err;
    }
  }

  getConnection(regionCode = region) {
    const connectionKey = Object.keys(Object.fromEntries(this.connections)).find(key =>
      key.startsWith(regionCode)
    );

    if (connectionKey) {
      return this.connections.get(connectionKey);
    }

    return this.defaultConnection;
  }

  async closeAllConnections() {
    for (const [key, connection] of this.connections) {
      await connection.close();
      console.log(`Closed connection: ${key}`);
    }
    this.connections.clear();
    this.defaultConnection = null;
  }
}

const dbManager = new DatabaseManager();

// Initialize default connection
dbManager.connectToRegion(region, process.env.MONGODB_URL, dbName)
  .catch((err) => {
    console.error('Failed to initialize default database connection:', err);
    process.exit(1);
  });

if (process.env.ENVIRONMENT === ENVIRONMENTS.STAGING) {
  mongoose.set("debug", true);
}

module.exports = {
  mongoose,
  dbManager,
  defaultConnection: () => dbManager.defaultConnection
};
