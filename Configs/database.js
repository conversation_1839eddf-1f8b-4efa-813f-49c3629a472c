const mongoose = require("mongoose");

const { ENVIRONMENTS } = require("./constants");

const dbName = process.env.DB_NAME;

// Apply timestamp plugin to all schemas
mongoose.plugin((schema) => {
  schema.set("timestamps", true); // Enable timestamps
  schema.set("versionKey", false); // Disable __v field
});

//BUILD A CONNECTION
mongoose
  .connect(process.env.MONGODB_URL, {
    dbName,
  })
  .then(() => {
    const message = `${dbName} database connected successfully :)`;
    console.log(message);
  })
  .catch((err) => console.log(err));

if (process.env.ENVIRONMENT === ENVIRONMENTS.STAGING) {
  mongoose.set("debug", true);
}

module.exports.mongoose = mongoose;
