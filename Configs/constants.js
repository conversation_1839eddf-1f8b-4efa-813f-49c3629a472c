const { default: slugify } = require("slugify");

//G<PERSON><PERSON><PERSON>L STATUS
exports.STATUS_CODES = {
    // 1XX INFORMATIONAL
    CONTINUE: 100,
    SWITCHING_PROTOCOLS: 101,
    PROCESSING: 102,
    EARLY_HINTS: 103,

    // 2XX SUCCESS
    SUCCESS: 200,
    CREATED: 201,
    ACCEPTED: 202,
    NON_AUTHORITATIVE_INFORMATION: 203,
    NO_CONTENT: 204,
    RESET_CONTENT: 205,
    PARTIAL_CONTENT: 206,
    MULTI_STATUS: 207,
    ALREADY_REPORTED: 208,
    IM_USED: 226,

    // 3XX REDIRECTION
    MULTIPLE_CHOICES: 300,
    MOVED_PERMANENTLY: 301,
    FOUND: 302,
    SEE_OTHER: 303,
    NOT_MODIFIED: 304,
    USE_PROXY: 305,
    TEMPORARY_REDIRECT: 307,
    PERMANENT_REDIRECT: 308,

    // 4XX CLIENT ERROR
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    PAYMENT_REQUIRED: 402,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    NOT_ALLOWED: 405,
    NOT_ACCEPTABLE: 406,
    PROXY_AUTHENTICATION_REQUIRED: 407,
    REQUEST_TIMEOUT: 408,
    CONFLICT: 409,
    GONE: 410,
    LENGTH_REQUIRED: 411,
    PRECONDITION_FAILED: 412,
    PAYLOAD_TOO_LARGE: 413,
    URI_TOO_LONG: 414,
    UNSUPPORTED_MEDIA_TYPE: 415,
    RANGE_NOT_SATISFIABLE: 416,
    EXPECTATION_FAILED: 417,
    UNPROCESSABLE_ENTITY: 422,
    VALIDATION_ERROR: 422,
    NOT_VALID_DATA: 422,
    LOCKED: 423,
    FAILED_DEPENDENCY: 424,
    UNORDERED_COLLECTION: 425,
    UPGRADE_REQUIRED: 426,
    PRECONDITION_REQUIRED: 428,
    TOO_MANY_REQUESTS: 429,
    REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
    UNAVAILABLE_FOR_LEGAL_REASONS: 451,

    // 5XX SERVER ERROR
    SERVER_ERROR: 500,
    NOT_IMPLEMENTED: 501,
    BAD_GATEWAY: 502,
    SERVICE_UNAVAILABLE: 503,
    GATEWAY_TIMEOUT: 504,
    HTTP_VERSION_NOT_SUPPORTED: 505,
    VARIANT_ALSO_NEGOTIATES: 506,
    INSUFFICIENT_STORAGE: 507,
    LOOP_DETECTED: 508,
    BANDWIDTH_LIMIT_EXCEEDED: 509,
    NOT_EXTENDED: 510,
    NETWORK_AUTHENTICATION_REQUIRED: 511,
};

exports.ADD_CUSTOMER_TOKEN = "add_customer";
exports.DEFAULT_CURRENCY = "USD";
exports.DEFAULT_TIMEZONE = "Asia/Amman";

exports.ENVIRONMENTS = {
    DEVELOPMENT: "development",
    STAGING: "staging",
    PRODUCTION: "production",
};

exports.DEVICE_TYPE = {
    ANDROID: "ANDROID",
    IOS: "IOS",
    WEB: "WEB"
};

exports.LOGIN_WITH = {
    NORMAL: "NORMAL",
    FACEBOOK: "FACEBOOK",
    GOOGLE: "GOOGLE",
    APPLE: "APPLE",
};

exports.DURATION_PERIOD_OPTIONS = {
    DAY: "DAY",
    MONTH: "MONTH"
}

exports.PATHS = {
    TEMP: "/temp",
    ORIGINAL: "/original",
    THUMB: "/thumb",
    USER_PROFILE: "/users/profile",
    USER_DOC_VERIFIED_ADMIN: "/user_doc_verified_admin",
    TRASH_FOLDER_BUCKET: "/trash"
}

exports.SIGNUP_TYPE = {
    NORMAL: "NORMAL",
    FACEBOOK: "FACEBOOK",
    GOOGLE: "GOOGLE",
    APPLE: "APPLE"
};

exports.VERIFICATION_STATUS = {
    PENDING: "PENDING",
    INITIATED: "INITIATED",
    STARTED: "STARTED",
    SUBMITTED: "SUBMITTED",
    APPROVED: "APPROVED",
    DECLINED: "DECLINED",
    RESUBMISSION: "RESUBMISSION",
    EXPIRED: "EXPIRED",
}

exports.GENDER = {
    MALE: "MALE",
    FEMALE: "FEMALE",
    OTHER: "OTHER",
}

exports.TEMPLATE_TYPE = {
    EMAIL: "EMAIL"
}

exports.CARD_TYPE = {
    CREDIT: "CREDIT",
    DEBIT: "DEBIT"
}

exports.COMMISSION_SLUG = {
    COMMISSION_FEE: "commissionFee",
    CARD_FEE: "cardFee",
    TRANSACTION_FEE: "transactionFee",
}

exports.STATIC_PAGE_SLUG = {
    PRIVACY_POLICY: "privacy-policy",
    TERMS_CONDITION: "terms-condition"
}

exports.MULTIPLIER_TYPE = {
    FLAT: "FLAT",
    PERCENTAGE: "PERCENTAGE",
}

exports.USER_TYPE = {
    USER: "user",
    ADMIN: "admin",
    SUB_ADMIN: "subAdmin"
};

exports.TRANSFER_TYPE = {
    BANK_TRANSFER: "bank transfer",
    CASH_PICKUP: "cash pickup",
    E_WALLET: "e-wallet"
}

exports.EMAIL_TYPE_SLUG = {
    FORGOT_PASSWORD: "forgotPassword",
    USER_KYC_VERIFICATION_DENIED: "userVerificationDenied",
    USER_TEMP_PASSWORD: "userTempPassword",
    RESUBMIT_VERIFICATION: "reSubmitUserVerification",
    QUARTERLY_REPORT: "quarterlyReport",
    USER_PAYMENT_STATUS: "userPaymentStatus",
    USER_WELCOME: "userWelcome",
    USER_ROLE_UPDATE: "userRoleUpdate",
}

exports.PAYMENT_STATUS_TYPE = {
    INITIATED: "INITIATED",
    PROCESSING: "PROCESSING",
    TRANSFER: "TRANSFER",
    COMPLETED: "COMPLETED"
}

exports.LOG_ACTION_TYPE = {
    CREATE: "CREATE",
    UPDATE: "UPDATE",
    DELETE: "DELETE",
    LOGIN: "LOGIN",
    LOGOUT: "LOGOUT"
}

exports.DEFAULT_LIMIT = 10;

exports.USER_IMPORT_LIMIT = 30;

exports.ACTIONS = {
    VIEW: "view",
    EDIT: "edit",
    DELETE: "delete",
    CREATE: "add",
};

exports.MODULE_NAME_SLUG = {
    userManagement: "User Management",
    staticPage: "Static Pages",
    transaction: "Transactions",
    logManagement: "Log management",
    commissionManagement: "Commission Management",
    stateManagement: "State Management",
    transactionLimit: "Transaction Limit",
    authorization: "Roles And Permission",
    adminManagement: "Admin Management",
    faqManagement: "FAQ Management",
    emailTemplates: "Email Templates"
}

exports.MODULE_NAME_SLUG = Object.entries(this.MODULE_NAME_SLUG).reduce((newObject, val) => {
    newObject[val[0]] = slugify(val[1]);
    return newObject;
}, {});