# Frontend Integration Guide for Multi-Regional AmalPay

## Overview

This guide explains how to integrate the frontend application with the multi-regional backend architecture, including region detection, dynamic API routing, and user experience considerations.

## Architecture Overview

```
Frontend (Single App)
├── Region Detection
├── Dynamic API Client
├── Regional Routing
├── Regional UI Components
└── Error Handling
```

## Region Detection

### 1. Automatic Region Detection

```javascript
// utils/regionDetection.js
export class RegionDetector {
    static regionMappings = {
        // US States
        'TX': 'texas',
        'CA': 'california',
        'FL': 'florida',
        'NY': 'newyork',
        
        // Canadian Provinces
        'ON': 'canada',
        'QC': 'canada',
        'BC': 'canada',
        
        // Countries
        'US': 'usa',
        'CA': 'canada',
        'MX': 'mexico'
    };

    static async detectUserRegion() {
        try {
            // Method 1: Geolocation API
            const position = await this.getCurrentPosition();
            const location = await this.reverseGeocode(position);
            
            // Method 2: IP-based detection (fallback)
            if (!location) {
                const ipLocation = await this.getLocationByIP();
                return this.mapLocationToRegion(ipLocation);
            }
            
            return this.mapLocationToRegion(location);
        } catch (error) {
            console.warn('Region detection failed:', error);
            return 'default';
        }
    }

    static getCurrentPosition() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                reject(new Error('Geolocation not supported'));
                return;
            }

            navigator.geolocation.getCurrentPosition(resolve, reject, {
                timeout: 10000,
                enableHighAccuracy: false
            });
        });
    }

    static async reverseGeocode(position) {
        const { latitude, longitude } = position.coords;
        
        // Use a geocoding service (Google, Mapbox, etc.)
        const response = await fetch(
            `https://api.mapbox.com/geocoding/v5/mapbox.places/${longitude},${latitude}.json?access_token=${MAPBOX_TOKEN}`
        );
        
        const data = await response.json();
        const place = data.features[0];
        
        return {
            state: this.extractState(place),
            country: this.extractCountry(place)
        };
    }

    static async getLocationByIP() {
        try {
            const response = await fetch('https://ipapi.co/json/');
            const data = await response.json();
            
            return {
                state: data.region_code,
                country: data.country_code
            };
        } catch (error) {
            console.warn('IP location detection failed:', error);
            return null;
        }
    }

    static mapLocationToRegion(location) {
        if (!location) return 'default';
        
        // Check state first
        if (location.state && this.regionMappings[location.state]) {
            return this.regionMappings[location.state];
        }
        
        // Fallback to country
        if (location.country && this.regionMappings[location.country]) {
            return this.regionMappings[location.country];
        }
        
        return 'default';
    }
}
```

### 2. Manual Region Selection

```javascript
// components/RegionSelector.jsx
import React, { useState, useEffect } from 'react';
import { RegionDetector } from '../utils/regionDetection';

const RegionSelector = ({ onRegionChange, initialRegion }) => {
    const [selectedRegion, setSelectedRegion] = useState(initialRegion);
    const [availableRegions] = useState([
        { code: 'texas', name: 'Texas', flag: '🇺🇸' },
        { code: 'california', name: 'California', flag: '🇺🇸' },
        { code: 'canada', name: 'Canada', flag: '🇨🇦' },
        { code: 'default', name: 'United States', flag: '🇺🇸' }
    ]);

    useEffect(() => {
        if (!initialRegion) {
            RegionDetector.detectUserRegion().then(region => {
                setSelectedRegion(region);
                onRegionChange(region);
            });
        }
    }, [initialRegion, onRegionChange]);

    const handleRegionChange = (region) => {
        setSelectedRegion(region);
        onRegionChange(region);
        
        // Store in localStorage for persistence
        localStorage.setItem('selectedRegion', region);
    };

    return (
        <div className="region-selector">
            <label htmlFor="region-select">Select Your Region:</label>
            <select 
                id="region-select"
                value={selectedRegion} 
                onChange={(e) => handleRegionChange(e.target.value)}
                className="region-dropdown"
            >
                {availableRegions.map(region => (
                    <option key={region.code} value={region.code}>
                        {region.flag} {region.name}
                    </option>
                ))}
            </select>
        </div>
    );
};

export default RegionSelector;
```

## Dynamic API Client

### 1. Regional API Configuration

```javascript
// services/apiConfig.js
export const API_ENDPOINTS = {
    texas: {
        baseURL: 'https://texas.amalpay.com/api',
        region: 'texas',
        regionCode: 'TX',
        features: ['domestic_transfers', 'bill_payments']
    },
    california: {
        baseURL: 'https://california.amalpay.com/api',
        region: 'california',
        regionCode: 'CA',
        features: ['domestic_transfers', 'bill_payments', 'crypto_payments']
    },
    canada: {
        baseURL: 'https://canada.amalpay.com/api',
        region: 'canada',
        regionCode: 'CA',
        features: ['domestic_transfers', 'international_transfers', 'bill_payments']
    },
    default: {
        baseURL: 'https://amalpay.com/api',
        region: 'default',
        regionCode: 'US',
        features: ['domestic_transfers', 'international_transfers', 'bill_payments']
    }
};

export const getRegionalConfig = (region) => {
    return API_ENDPOINTS[region] || API_ENDPOINTS.default;
};
```

### 2. Dynamic API Client

```javascript
// services/apiClient.js
import axios from 'axios';
import { getRegionalConfig } from './apiConfig';

class RegionalAPIClient {
    constructor() {
        this.currentRegion = localStorage.getItem('selectedRegion') || 'default';
        this.client = null;
        this.initializeClient();
    }

    initializeClient() {
        const config = getRegionalConfig(this.currentRegion);
        
        this.client = axios.create({
            baseURL: config.baseURL,
            timeout: 30000,
            headers: {
                'Content-Type': 'application/json',
                'X-Region': config.region,
                'X-Region-Code': config.regionCode
            }
        });

        // Request interceptor
        this.client.interceptors.request.use(
            (config) => {
                const token = localStorage.getItem('authToken');
                if (token) {
                    config.headers.Authorization = `Bearer ${token}`;
                }
                return config;
            },
            (error) => Promise.reject(error)
        );

        // Response interceptor
        this.client.interceptors.response.use(
            (response) => response,
            (error) => {
                if (error.response?.status === 401 && error.response?.data?.errorCode === 'REGION_ACCESS_DENIED') {
                    this.handleRegionAccessDenied(error.response.data);
                }
                return Promise.reject(error);
            }
        );
    }

    switchRegion(newRegion) {
        this.currentRegion = newRegion;
        localStorage.setItem('selectedRegion', newRegion);
        this.initializeClient();
    }

    handleRegionAccessDenied(errorData) {
        // Redirect to correct regional portal
        if (errorData.details?.correctEndpoint) {
            const correctRegion = errorData.details.userRegion;
            window.location.href = `https://${correctRegion}.amalpay.com`;
        }
    }

    // API Methods
    async get(endpoint, params = {}) {
        const response = await this.client.get(endpoint, { params });
        return response.data;
    }

    async post(endpoint, data = {}) {
        const response = await this.client.post(endpoint, data);
        return response.data;
    }

    async put(endpoint, data = {}) {
        const response = await this.client.put(endpoint, data);
        return response.data;
    }

    async delete(endpoint) {
        const response = await this.client.delete(endpoint);
        return response.data;
    }

    // Regional-specific methods
    async getRegionInfo() {
        return this.get('/system/region-info');
    }

    async signIn(credentials) {
        return this.post('/auth/signIn', credentials);
    }

    async signUp(userData) {
        return this.post('/auth/signUp', userData);
    }

    async getUserProfile() {
        return this.get('/user/profile');
    }

    async getTransactions(params = {}) {
        return this.get('/transaction/history', params);
    }
}

// Singleton instance
export const apiClient = new RegionalAPIClient();
```

## React Context for Regional State

### 1. Regional Context Provider

```javascript
// contexts/RegionalContext.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiClient } from '../services/apiClient';
import { RegionDetector } from '../utils/regionDetection';

const RegionalContext = createContext();

export const useRegional = () => {
    const context = useContext(RegionalContext);
    if (!context) {
        throw new Error('useRegional must be used within RegionalProvider');
    }
    return context;
};

export const RegionalProvider = ({ children }) => {
    const [currentRegion, setCurrentRegion] = useState(
        localStorage.getItem('selectedRegion') || 'default'
    );
    const [regionInfo, setRegionInfo] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        initializeRegion();
    }, []);

    const initializeRegion = async () => {
        try {
            setLoading(true);
            
            // Auto-detect region if not set
            if (!localStorage.getItem('selectedRegion')) {
                const detectedRegion = await RegionDetector.detectUserRegion();
                setCurrentRegion(detectedRegion);
                localStorage.setItem('selectedRegion', detectedRegion);
            }

            // Switch API client to current region
            apiClient.switchRegion(currentRegion);

            // Fetch region information
            const info = await apiClient.getRegionInfo();
            setRegionInfo(info.data);
            
        } catch (err) {
            setError(err.message);
            console.error('Failed to initialize region:', err);
        } finally {
            setLoading(false);
        }
    };

    const switchRegion = async (newRegion) => {
        try {
            setLoading(true);
            setCurrentRegion(newRegion);
            localStorage.setItem('selectedRegion', newRegion);
            
            // Switch API client
            apiClient.switchRegion(newRegion);
            
            // Fetch new region info
            const info = await apiClient.getRegionInfo();
            setRegionInfo(info.data);
            
            // Clear user session (they'll need to re-login)
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    const value = {
        currentRegion,
        regionInfo,
        loading,
        error,
        switchRegion,
        isFeatureEnabled: (feature) => {
            return regionInfo?.features?.[feature] || false;
        },
        getRegionalConfig: () => {
            return {
                region: regionInfo?.region,
                regionCode: regionInfo?.regionCode,
                regionName: regionInfo?.regionName,
                allowedStates: regionInfo?.allowedStates || [],
                allowedCountries: regionInfo?.allowedCountries || []
            };
        }
    };

    return (
        <RegionalContext.Provider value={value}>
            {children}
        </RegionalContext.Provider>
    );
};
```

## Regional UI Components

### 1. Regional Header Component

```javascript
// components/RegionalHeader.jsx
import React from 'react';
import { useRegional } from '../contexts/RegionalContext';
import RegionSelector from './RegionSelector';

const RegionalHeader = () => {
    const { regionInfo, currentRegion, switchRegion } = useRegional();

    return (
        <header className="regional-header">
            <div className="header-content">
                <div className="logo-section">
                    <img src="/logo.png" alt="AmalPay" />
                    {regionInfo && (
                        <span className="region-badge">
                            {regionInfo.regionName}
                        </span>
                    )}
                </div>
                
                <div className="region-controls">
                    <RegionSelector 
                        onRegionChange={switchRegion}
                        initialRegion={currentRegion}
                    />
                </div>
            </div>
            
            {regionInfo && (
                <div className="region-info-bar">
                    <span>
                        Serving: {regionInfo.allowedStates?.join(', ')} | 
                        {regionInfo.allowedCountries?.join(', ')}
                    </span>
                </div>
            )}
        </header>
    );
};

export default RegionalHeader;
```

### 2. Regional Feature Gates

```javascript
// components/FeatureGate.jsx
import React from 'react';
import { useRegional } from '../contexts/RegionalContext';

const FeatureGate = ({ feature, children, fallback = null }) => {
    const { isFeatureEnabled } = useRegional();

    if (!isFeatureEnabled(feature)) {
        return fallback;
    }

    return children;
};

// Usage example
const TransactionPage = () => {
    return (
        <div>
            <h1>Send Money</h1>
            
            <FeatureGate feature="domestic_transfers">
                <DomesticTransferForm />
            </FeatureGate>
            
            <FeatureGate 
                feature="international_transfers"
                fallback={
                    <div className="feature-unavailable">
                        International transfers not available in your region
                    </div>
                }
            >
                <InternationalTransferForm />
            </FeatureGate>
            
            <FeatureGate feature="crypto_payments">
                <CryptoPaymentOption />
            </FeatureGate>
        </div>
    );
};
```

## Error Handling

### 1. Regional Error Handler

```javascript
// utils/errorHandler.js
export class RegionalErrorHandler {
    static handle(error) {
        if (error.response?.data?.errorCode) {
            switch (error.response.data.errorCode) {
                case 'REGION_ACCESS_DENIED':
                    return this.handleRegionAccessDenied(error.response.data);
                
                case 'REGION_LOCATION_INVALID':
                    return this.handleInvalidLocation(error.response.data);
                
                case 'REGION_SERVICE_UNAVAILABLE':
                    return this.handleServiceUnavailable(error.response.data);
                
                default:
                    return this.handleGenericError(error);
            }
        }
        
        return this.handleGenericError(error);
    }

    static handleRegionAccessDenied(errorData) {
        const userRegion = errorData.details?.userRegion;
        const correctEndpoint = errorData.details?.correctEndpoint;
        
        if (userRegion && correctEndpoint) {
            const message = `You need to access AmalPay through your regional portal: ${userRegion}`;
            
            return {
                type: 'region_redirect',
                message,
                action: () => {
                    window.location.href = correctEndpoint.replace('/api', '');
                }
            };
        }
        
        return {
            type: 'error',
            message: errorData.message
        };
    }

    static handleInvalidLocation(errorData) {
        return {
            type: 'location_error',
            message: errorData.message,
            action: () => {
                // Show region selector or location update form
            }
        };
    }

    static handleServiceUnavailable(errorData) {
        return {
            type: 'service_error',
            message: 'Regional service temporarily unavailable. Please try again later.',
            retryable: true
        };
    }

    static handleGenericError(error) {
        return {
            type: 'error',
            message: error.message || 'An unexpected error occurred'
        };
    }
}
```

## Routing and Navigation

### 1. Regional Route Protection

```javascript
// components/RegionalRoute.jsx
import React from 'react';
import { Navigate } from 'react-router-dom';
import { useRegional } from '../contexts/RegionalContext';

const RegionalRoute = ({ children, requiredFeatures = [], allowedRegions = [] }) => {
    const { currentRegion, regionInfo, isFeatureEnabled } = useRegional();

    // Check if current region is allowed
    if (allowedRegions.length > 0 && !allowedRegions.includes(currentRegion)) {
        return <Navigate to="/region-not-supported" replace />;
    }

    // Check if required features are available
    for (const feature of requiredFeatures) {
        if (!isFeatureEnabled(feature)) {
            return <Navigate to="/feature-not-available" replace />;
        }
    }

    return children;
};

// Usage in App.jsx
const App = () => {
    return (
        <RegionalProvider>
            <Router>
                <Routes>
                    <Route path="/" element={<HomePage />} />
                    
                    <Route 
                        path="/international-transfer" 
                        element={
                            <RegionalRoute requiredFeatures={['international_transfers']}>
                                <InternationalTransferPage />
                            </RegionalRoute>
                        } 
                    />
                    
                    <Route 
                        path="/crypto" 
                        element={
                            <RegionalRoute 
                                requiredFeatures={['crypto_payments']}
                                allowedRegions={['california']}
                            >
                                <CryptoPage />
                            </RegionalRoute>
                        } 
                    />
                </Routes>
            </Router>
        </RegionalProvider>
    );
};
```

## Performance Optimization

### 1. Regional Caching

```javascript
// utils/regionalCache.js
class RegionalCache {
    constructor() {
        this.cache = new Map();
        this.ttl = 5 * 60 * 1000; // 5 minutes
    }

    set(region, key, data) {
        const cacheKey = `${region}:${key}`;
        this.cache.set(cacheKey, {
            data,
            timestamp: Date.now()
        });
    }

    get(region, key) {
        const cacheKey = `${region}:${key}`;
        const cached = this.cache.get(cacheKey);
        
        if (!cached) return null;
        
        if (Date.now() - cached.timestamp > this.ttl) {
            this.cache.delete(cacheKey);
            return null;
        }
        
        return cached.data;
    }

    clear(region) {
        for (const key of this.cache.keys()) {
            if (key.startsWith(`${region}:`)) {
                this.cache.delete(key);
            }
        }
    }
}

export const regionalCache = new RegionalCache();
```

---

*Last Updated: 2025-01-29*
*Version: 1.0*
