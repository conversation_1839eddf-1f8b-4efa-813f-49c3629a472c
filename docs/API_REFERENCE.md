# AmalPay Multi-Regional API Reference

## Overview

This document describes the API changes and new endpoints introduced for the multi-regional architecture.

## Base URLs

### Regional Endpoints
```
Texas:      https://texas.amalpay.com/api
California: https://california.amalpay.com/api
Canada:     https://canada.amalpay.com/api
Default:    https://amalpay.com/api
```

## Headers

### Regional Headers
```http
X-Region: texas              # Optional region specification
X-Region-Code: TX           # Regional code
Accept-Language: en         # Language preference
Authorization: Bearer <token> # JWT token (region-specific)
```

## Authentication Endpoints

### Sign In (Enhanced)

**Endpoint:** `POST /auth/signIn`

**Request:**
```json
{
    "email": "<EMAIL>",
    "password": "password123",
    "deviceId": "device-uuid",
    "deviceType": "web",
    "timezone": "America/Chicago",
    "appVersion": "1.0.0"
}
```

**Response (Success):**
```json
{
    "success": true,
    "data": {
        "_id": "user-id",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "region": "texas",
        "regionCode": "TX",
        "state": "Texas",
        "country": "United States",
        "roleId": {
            "_id": "role-id",
            "name": "User",
            "slug": "user"
        },
        "authToken": "jwt-token",
        "changePasswordAvailable": true
    }
}
```

**Response (Cross-Region Error):**
```json
{
    "success": false,
    "message": "Access denied. Please login through california region portal.",
    "code": 401
}
```

### Sign Up (Enhanced)

**Endpoint:** `POST /auth/signUp`

**Request:**
```json
{
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "password": "password123",
    "state": "Texas",
    "country": "United States",
    "city": "Houston",
    "phoneNumber": "+1234567890",
    "countryCode": "+1",
    "deviceId": "device-uuid",
    "deviceType": "web",
    "timezone": "America/Chicago",
    "appVersion": "1.0.0"
}
```

**Response (Success):**
```json
{
    "success": true,
    "data": {
        "_id": "user-id",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "region": "texas",
        "regionCode": "TX",
        "state": "Texas",
        "country": "United States",
        "authToken": "jwt-token"
    }
}
```

**Response (Location Not Allowed):**
```json
{
    "success": false,
    "message": "Registration not allowed. Texas region only accepts users from: States: Texas, Countries: United States",
    "code": 400
}
```

## Regional System Endpoints

### Get Region Information

**Endpoint:** `GET /system/region-info`

**Response:**
```json
{
    "success": true,
    "data": {
        "region": "texas",
        "regionCode": "TX",
        "regionName": "Texas",
        "allowedStates": ["Texas"],
        "allowedCountries": ["United States"],
        "features": {
            "multiCurrency": false,
            "internationalTransfers": false,
            "localTransfersOnly": true
        }
    }
}
```

### Health Check

**Endpoint:** `GET /health`

**Response:**
```json
{
    "status": "healthy",
    "region": "texas",
    "regionCode": "TX",
    "timestamp": "2025-01-29T10:30:00.000Z",
    "database": "connected",
    "services": {
        "email": "operational",
        "payment": "operational",
        "verification": "operational"
    }
}
```

### Regional Statistics

**Endpoint:** `GET /admin/regional-stats`
**Authentication:** Admin only

**Response:**
```json
{
    "success": true,
    "data": {
        "region": "texas",
        "stats": {
            "totalUsers": 1250,
            "activeUsers": 980,
            "totalTransactions": 5420,
            "totalVolume": 2450000.50,
            "newUsersToday": 15,
            "transactionsToday": 89
        },
        "lastUpdated": "2025-01-29T10:30:00.000Z"
    }
}
```

## User Management Endpoints

### Get User Profile (Enhanced)

**Endpoint:** `GET /user/profile`
**Authentication:** Required

**Response:**
```json
{
    "success": true,
    "data": {
        "_id": "user-id",
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "region": "texas",
        "regionCode": "TX",
        "state": "Texas",
        "country": "United States",
        "city": "Houston",
        "kycStatus": "approved",
        "isActive": true,
        "createdAt": "2025-01-15T08:00:00.000Z",
        "regionalInfo": {
            "allowedOperations": ["domestic_transfer", "bill_payment"],
            "restrictions": ["international_transfer"],
            "complianceLevel": "state_compliant"
        }
    }
}
```

### Update User Profile (Regional Validation)

**Endpoint:** `PUT /user/profile`
**Authentication:** Required

**Request:**
```json
{
    "firstName": "John",
    "lastName": "Smith",
    "state": "Texas",
    "city": "Dallas"
}
```

**Response (Location Change Not Allowed):**
```json
{
    "success": false,
    "message": "Cannot change location to a state outside Texas region",
    "code": 400
}
```

## Transaction Endpoints

### Create Transaction (Regional)

**Endpoint:** `POST /transaction/create`
**Authentication:** Required

**Request:**
```json
{
    "recipientId": "recipient-id",
    "amount": 500.00,
    "currency": "USD",
    "purpose": "Family Support",
    "paymentMethod": "bank_transfer"
}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "_id": "transaction-id",
        "userId": "user-id",
        "recipientId": "recipient-id",
        "amount": 500.00,
        "currency": "USD",
        "status": "pending",
        "region": "texas",
        "regionalCompliance": {
            "reportingRequired": true,
            "maxAmount": 10000.00,
            "additionalVerification": false
        },
        "createdAt": "2025-01-29T10:30:00.000Z"
    }
}
```

### Get Regional Transaction History

**Endpoint:** `GET /transaction/history`
**Authentication:** Required

**Query Parameters:**
```
?page=1&limit=20&startDate=2025-01-01&endDate=2025-01-29&status=completed
```

**Response:**
```json
{
    "success": true,
    "data": {
        "transactions": [
            {
                "_id": "transaction-id",
                "amount": 500.00,
                "currency": "USD",
                "status": "completed",
                "region": "texas",
                "createdAt": "2025-01-29T10:30:00.000Z"
            }
        ],
        "pagination": {
            "currentPage": 1,
            "totalPages": 5,
            "totalTransactions": 89,
            "hasMore": true
        },
        "regionalSummary": {
            "totalAmount": 45000.00,
            "averageAmount": 505.62,
            "transactionCount": 89
        }
    }
}
```

## Admin Endpoints

### Regional User Management

**Endpoint:** `GET /admin/users`
**Authentication:** Admin only

**Query Parameters:**
```
?page=1&limit=50&state=Texas&userSource=web&sortBy=createdAt&order=desc
```

**Response:**
```json
{
    "success": true,
    "data": {
        "users": [
            {
                "_id": "user-id",
                "firstName": "John",
                "lastName": "Doe",
                "email": "<EMAIL>",
                "region": "texas",
                "state": "Texas",
                "kycStatus": "approved",
                "totalTransactions": 15,
                "totalTransferAmount": 7500.00,
                "createdAt": "2025-01-15T08:00:00.000Z"
            }
        ],
        "total": 1250,
        "hasMore": true,
        "regionalFilters": {
            "availableStates": ["Texas"],
            "availableCountries": ["United States"],
            "userSources": ["web", "mobile", "referral"]
        }
    }
}
```

### Cross-Region Access Logs

**Endpoint:** `GET /admin/access-logs`
**Authentication:** Super Admin only

**Response:**
```json
{
    "success": true,
    "data": {
        "accessAttempts": [
            {
                "userId": "user-id",
                "userRegion": "california",
                "attemptedRegion": "texas",
                "timestamp": "2025-01-29T10:30:00.000Z",
                "ipAddress": "***********",
                "userAgent": "Mozilla/5.0...",
                "blocked": true,
                "reason": "cross_region_access"
            }
        ],
        "summary": {
            "totalAttempts": 25,
            "blockedAttempts": 25,
            "successfulAttempts": 0
        }
    }
}
```

## Error Codes

### Regional Error Codes

| Code | Message | Description |
|------|---------|-------------|
| 4001 | REGION_ACCESS_DENIED | User trying to access wrong region |
| 4002 | REGION_LOCATION_INVALID | User location not allowed in region |
| 4003 | REGION_NOT_FOUND | Invalid region specified |
| 4004 | REGION_SERVICE_UNAVAILABLE | Regional service temporarily down |
| 4005 | CROSS_REGION_OPERATION_DENIED | Operation not allowed across regions |

### Example Error Response

```json
{
    "success": false,
    "message": "Access denied. Please login through california region portal.",
    "code": 4001,
    "errorCode": "REGION_ACCESS_DENIED",
    "details": {
        "userRegion": "california",
        "attemptedRegion": "texas",
        "correctEndpoint": "https://california.amalpay.com/api"
    }
}
```

## Rate Limiting

### Regional Rate Limits

```
Authentication Endpoints: 5 requests/minute per IP per region
Transaction Endpoints: 10 requests/minute per user per region
Admin Endpoints: 100 requests/minute per admin per region
```

### Rate Limit Headers

```http
X-RateLimit-Limit: 10
X-RateLimit-Remaining: 7
X-RateLimit-Reset: 1643723400
X-RateLimit-Region: texas
```

## Webhooks

### Regional Webhook Configuration

**Endpoint Configuration:**
```json
{
    "region": "texas",
    "webhookUrl": "https://client.example.com/webhooks/texas",
    "events": ["transaction.completed", "user.verified"],
    "secret": "webhook-secret"
}
```

**Webhook Payload:**
```json
{
    "event": "transaction.completed",
    "region": "texas",
    "regionCode": "TX",
    "data": {
        "transactionId": "transaction-id",
        "userId": "user-id",
        "amount": 500.00,
        "status": "completed"
    },
    "timestamp": "2025-01-29T10:30:00.000Z"
}
```

## SDK Integration

### JavaScript SDK Example

```javascript
import AmalPaySDK from '@amalpay/sdk';

// Initialize with region
const amalPay = new AmalPaySDK({
    region: 'texas',
    apiKey: 'your-api-key',
    environment: 'production'
});

// Automatic regional routing
const user = await amalPay.auth.signIn({
    email: '<EMAIL>',
    password: 'password'
});

// Regional transaction
const transaction = await amalPay.transactions.create({
    recipientId: 'recipient-id',
    amount: 500.00
});
```

---

*Last Updated: 2025-01-29*
*Version: 1.0*
