# AmalPay Multi-Regional Architecture Documentation

## Overview

This document outlines the implementation of a multi-regional backend architecture for AmalPay, allowing separate login systems and reporting for different states/countries while maintaining a unified frontend experience.

## Architecture Design

### Core Concept
- **Separate MongoDB databases** for each region (Texas, California, etc.)
- **Separate AWS deployments** per region
- **Unified frontend** that dynamically connects to appropriate regional backend
- **Regional data isolation** with cross-region access controls

### Regional Structure
```
AmalPay Multi-Regional Architecture
├── Frontend (Unified)
│   ├── Region Selection
│   └── Dynamic API Routing
├── Backend Instances
│   ├── Texas Region (texas.amalpay.com)
│   ├── California Region (california.amalpay.com)
│   ├── Canada Region (canada.amalpay.com)
│   └── Default/USA Region (amalpay.com)
└── Databases
    ├── amal_pay_texas
    ├── amal_pay_california
    ├── amal_pay_canada
    └── amal_pay_production (default)
```

## Implementation Components

### 1. Environment Configuration

Each region has its own environment file:

**Example: `.env.texas`**
```env
PROJECT_NAME="AmalPay Texas"
REGION="texas"
REGION_CODE="TX"
REGION_NAME="Texas"
DB_NAME=amal_pay_texas
WEB_URL="https://texas.amalpay.com"
ALLOWED_STATES=["Texas"]
ALLOWED_COUNTRIES=["United States"]
```

### 2. Database Management

**Enhanced Database Configuration (`Configs/database.js`)**
- `DatabaseManager` class for handling multiple regional connections
- Dynamic connection switching based on region
- Connection pooling and management

**Key Features:**
- `connectToRegion(regionCode, mongoUrl, databaseName)` - Connect to specific regional database
- `getConnection(regionCode)` - Retrieve regional connection
- `closeAllConnections()` - Cleanup all connections

### 3. User Schema Enhancement

**Added Regional Fields to User Schema:**
```javascript
region: {
    type: String,
    required: true,
    default: process.env.REGION || 'default',
    index: true
},
regionCode: {
    type: String,
    uppercase: true,
    default: process.env.REGION_CODE || 'DEFAULT'
}
```

### 4. Regional Authentication

**New Middleware: `Middlewares/regionalAuthentication.js`**

**Key Methods:**
- `validateRegion()` - Ensures user belongs to current region
- `setRegionalConnection()` - Sets appropriate database connection
- `authenticateWithRegion()` - Enhanced auth with region validation
- `validateUserLocation()` - Validates user's state/country against region rules

### 5. Enhanced Authentication Flow

**Updated AuthController with Regional Logic:**
- **Sign In**: Validates user belongs to current region
- **Sign Up**: Validates location and assigns region automatically
- **Cross-Region Protection**: Prevents access from wrong regional portal

## Deployment Strategy

### 1. Infrastructure Setup

**Per Region Requirements:**
- Separate MongoDB cluster
- Separate AWS EC2/ECS instance
- Regional subdomain (texas.amalpay.com, california.amalpay.com)
- Regional SSL certificates
- Regional environment variables

### 2. Database Migration

**For Existing Users:**
```javascript
// Migration script example
const migrateUsersToRegions = async () => {
    const users = await UserModel.find({});
    
    for (const user of users) {
        const region = determineUserRegion(user.state, user.country);
        await UserModel.updateOne(
            { _id: user._id },
            { 
                region: region,
                regionCode: getRegionCode(region)
            }
        );
    }
};
```

### 3. Frontend Integration

**Region Selection Implementation:**
```javascript
// Frontend region detection/selection
const detectUserRegion = (userState, userCountry) => {
    const regionMappings = {
        'Texas': 'texas',
        'California': 'california',
        'Canada': 'canada'
    };
    
    return regionMappings[userState] || 'default';
};

// Dynamic API endpoint
const getApiEndpoint = (region) => {
    const endpoints = {
        'texas': 'https://texas.amalpay.com/api',
        'california': 'https://california.amalpay.com/api',
        'canada': 'https://canada.amalpay.com/api',
        'default': 'https://amalpay.com/api'
    };
    
    return endpoints[region] || endpoints.default;
};
```

## Security Considerations

### 1. Cross-Region Access Prevention
- Users can only access their assigned regional backend
- Authentication tokens are region-specific
- Database connections are isolated per region

### 2. Data Isolation
- Complete data separation between regions
- No cross-region data queries
- Regional reporting and analytics

### 3. Regional Compliance
- Each region can have specific compliance rules
- Regional data residency requirements
- Local regulatory compliance

## API Changes

### 1. New Headers
```
X-Region: texas          // Optional region specification
X-Region-Code: TX        // Regional code
```

### 2. Enhanced Endpoints

**Region Info Endpoint:**
```
GET /api/system/region-info
Response: {
    region: "texas",
    regionCode: "TX",
    regionName: "Texas",
    allowedStates: ["Texas"],
    allowedCountries: ["United States"]
}
```

**Regional User Registration:**
```
POST /api/auth/signUp
Body: {
    ...userDetails,
    state: "Texas",        // Validated against region
    country: "United States"
}
```

### 3. Error Responses

**Cross-Region Access:**
```json
{
    "success": false,
    "message": "Access denied. Please login through texas region portal.",
    "code": 401
}
```

**Invalid Location:**
```json
{
    "success": false,
    "message": "Registration not allowed. Texas region only accepts users from: States: Texas, Countries: United States",
    "code": 400
}
```

## Monitoring and Logging

### 1. Regional Metrics
- Per-region user counts
- Regional transaction volumes
- Cross-region access attempts

### 2. Enhanced Logging
```javascript
// Regional log format
console.log(`[${process.env.REGION}] User ${userId} authenticated successfully`);
```

### 3. Health Checks
- Regional database connectivity
- Cross-region communication status
- Regional service health

## Testing Strategy

### 1. Unit Tests
- Regional authentication logic
- Database connection management
- User location validation

### 2. Integration Tests
- Cross-region access prevention
- Regional data isolation
- Frontend-backend region coordination

### 3. End-to-End Tests
- Complete user journey per region
- Region switching scenarios
- Error handling flows

## Maintenance and Operations

### 1. Regional Deployments
- Independent deployment per region
- Regional rollback capabilities
- Region-specific configuration management

### 2. Database Maintenance
- Regional backup strategies
- Cross-region data synchronization (if needed)
- Regional performance monitoring

### 3. Scaling Considerations
- Regional load balancing
- Regional auto-scaling
- Cross-region disaster recovery

## Future Enhancements

### 1. Advanced Features
- Cross-region user transfers
- Regional admin dashboards
- Multi-region reporting aggregation

### 2. Technical Improvements
- Regional caching strategies
- Advanced regional routing
- Regional API rate limiting

### 3. Business Features
- Regional pricing models
- Regional payment methods
- Regional compliance modules

---

## Quick Start Guide

### 1. Setup New Region
1. Create regional environment file (`.env.{region}`)
2. Setup regional MongoDB database
3. Deploy regional backend instance
4. Configure regional subdomain
5. Update frontend region mappings

### 2. Test Regional Setup
1. Access regional URL
2. Attempt user registration with regional location
3. Verify cross-region access is blocked
4. Test regional reporting

### 3. Monitor Regional Health
1. Check regional database connections
2. Monitor regional user activity
3. Verify regional data isolation

---

*Last Updated: 2025-01-29*
*Version: 1.0*
