# AmalPay Multi-Regional Architecture Documentation

## 📋 Table of Contents

1. [Overview](#overview)
2. [Quick Start](#quick-start)
3. [Documentation Index](#documentation-index)
4. [Architecture Summary](#architecture-summary)
5. [Implementation Status](#implementation-status)
6. [Getting Help](#getting-help)

## 🌍 Overview

AmalPay Multi-Regional Architecture enables separate backend instances for different states and countries while maintaining a unified frontend experience. Each region operates independently with its own database, user base, and compliance rules.

### Key Benefits

- **Regional Compliance**: Meet local regulatory requirements
- **Data Isolation**: Complete separation of regional data
- **Scalability**: Independent scaling per region
- **User Experience**: Seamless regional switching
- **Reporting**: Region-specific analytics and reporting

### Supported Regions

| Region | Code | Subdomain | Database |
|--------|------|-----------|----------|
| Texas | TX | texas.amalpay.com | amal_pay_texas |
| California | CA | california.amalpay.com | amal_pay_california |
| Canada | CA | canada.amalpay.com | amal_pay_canada |
| Default/USA | US | amalpay.com | amal_pay_production |

## 🚀 Quick Start

### Prerequisites

- Node.js 16+
- MongoDB 5.0+
- AWS Account
- Domain management access

### 1. Clone and Setup

```bash
git clone <repository-url>
cd API
npm install
```

### 2. Configure Regional Environment

```bash
# Copy regional environment template
cp .env.texas .env.your-region

# Edit regional configuration
nano .env.your-region
```

### 3. Setup Regional Database

```bash
# Seed regional database
NODE_ENV=your-region npm run seed
```

### 4. Start Regional Instance

```bash
# Start regional backend
NODE_ENV=your-region npm start
```

### 5. Test Regional Setup

```bash
# Test health endpoint
curl https://your-region.amalpay.com/api/health

# Test region info
curl https://your-region.amalpay.com/api/system/region-info
```

## 📚 Documentation Index

### Core Documentation

| Document | Description | Audience |
|----------|-------------|----------|
| [**Multi-Regional Architecture**](./MULTI_REGIONAL_ARCHITECTURE.md) | Complete architecture overview and design decisions | Architects, Developers |
| [**Deployment Guide**](./DEPLOYMENT_GUIDE.md) | Step-by-step deployment instructions | DevOps, System Admins |
| [**API Reference**](./API_REFERENCE.md) | Regional API endpoints and changes | Frontend Developers, Integrators |
| [**Frontend Integration**](./FRONTEND_INTEGRATION.md) | Frontend implementation guide | Frontend Developers |

### Quick Reference

- **Environment Setup**: See [Deployment Guide - Phase 2](./DEPLOYMENT_GUIDE.md#phase-2-environment-configuration)
- **Database Migration**: See [Deployment Guide - Phase 6](./DEPLOYMENT_GUIDE.md#phase-6-data-migration)
- **API Changes**: See [API Reference](./API_REFERENCE.md)
- **Error Handling**: See [API Reference - Error Codes](./API_REFERENCE.md#error-codes)

## 🏗️ Architecture Summary

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (Unified)                       │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │ Region Detection│ │ Dynamic Routing │ │ Feature Gates   ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
┌───────────────▼──┐ ┌──────────▼──┐ ┌─────────▼──────┐
│ Texas Backend    │ │ CA Backend  │ │ Canada Backend │
│ texas.amalpay.com│ │california.  │ │canada.amalpay. │
│                  │ │amalpay.com  │ │com             │
└───────────────┬──┘ └──────────┬──┘ └─────────┬──────┘
                │               │              │
┌───────────────▼──┐ ┌──────────▼──┐ ┌─────────▼──────┐
│ MongoDB Texas    │ │ MongoDB CA  │ │ MongoDB Canada │
│ amal_pay_texas   │ │amal_pay_ca  │ │ amal_pay_canada│
└──────────────────┘ └─────────────┘ └────────────────┘
```

### Key Components

1. **Regional Database Manager**: Handles multiple database connections
2. **Regional Authentication**: Validates users belong to correct region
3. **Regional Middleware**: Enforces regional access controls
4. **Dynamic API Client**: Frontend automatically routes to correct backend
5. **Feature Gates**: Enable/disable features per region

## 📊 Implementation Status

### ✅ Completed Components

- [x] Database configuration with regional support
- [x] Regional environment files (Texas, California)
- [x] User schema enhancement with region fields
- [x] Regional authentication middleware
- [x] Enhanced AuthController with region validation
- [x] Comprehensive documentation

### 🚧 In Progress

- [ ] Frontend integration components
- [ ] Regional deployment scripts
- [ ] Data migration utilities
- [ ] Testing framework

### 📋 Pending Tasks

- [ ] AWS infrastructure setup
- [ ] SSL certificate configuration
- [ ] Regional monitoring setup
- [ ] Performance optimization
- [ ] Security audit

## 🔧 Configuration Examples

### Regional Environment Variables

```env
# Core Regional Settings
REGION="texas"
REGION_CODE="TX"
REGION_NAME="Texas"
DB_NAME="amal_pay_texas"

# Regional Restrictions
ALLOWED_STATES=["Texas"]
ALLOWED_COUNTRIES=["United States"]

# Regional URLs
WEB_URL="https://texas.amalpay.com"
EMAIL_ADDRESS="<EMAIL>"
```

### Frontend Region Detection

```javascript
// Automatic region detection
const region = await RegionDetector.detectUserRegion();

// Manual region selection
<RegionSelector onRegionChange={switchRegion} />

// Feature-based routing
<FeatureGate feature="international_transfers">
  <InternationalTransferForm />
</FeatureGate>
```

### API Usage

```javascript
// Regional API client
const apiClient = new RegionalAPIClient();
apiClient.switchRegion('texas');

// Regional authentication
const user = await apiClient.signIn({
  email: '<EMAIL>',
  password: 'password'
});
```

## 🚨 Important Considerations

### Security

- **Cross-Region Access**: Automatically blocked
- **Data Isolation**: Complete separation between regions
- **Regional Tokens**: JWT tokens are region-specific

### Performance

- **Regional Caching**: Implement regional data caching
- **CDN Configuration**: Use regional CDN endpoints
- **Database Optimization**: Index regional queries

### Compliance

- **Data Residency**: Data stays within regional boundaries
- **Local Regulations**: Each region can have specific rules
- **Audit Trails**: Regional activity logging

## 🆘 Getting Help

### Common Issues

1. **Cross-Region Access Denied**
   - Check user's region assignment
   - Verify correct regional endpoint
   - See [API Reference - Error Codes](./API_REFERENCE.md#regional-error-codes)

2. **Database Connection Issues**
   - Verify regional environment variables
   - Check MongoDB connection strings
   - See [Deployment Guide - Database Setup](./DEPLOYMENT_GUIDE.md#phase-1-database-setup)

3. **Frontend Region Detection**
   - Check geolocation permissions
   - Verify IP detection fallback
   - See [Frontend Integration](./FRONTEND_INTEGRATION.md#region-detection)

### Support Channels

- **Technical Issues**: Create GitHub issue with `[REGIONAL]` prefix
- **Deployment Help**: Check deployment guide or contact DevOps team
- **API Questions**: Refer to API reference documentation

### Development Workflow

1. **Local Development**: Use `.env.development` with `REGION=default`
2. **Regional Testing**: Use regional environment files
3. **Staging**: Deploy to regional staging environments
4. **Production**: Deploy to regional production instances

## 📈 Monitoring and Metrics

### Key Metrics to Track

- Regional user distribution
- Cross-region access attempts
- Regional API performance
- Database connection health per region
- Regional transaction volumes

### Monitoring Setup

```javascript
// Regional health check
GET /api/health
{
  "status": "healthy",
  "region": "texas",
  "database": "connected",
  "timestamp": "2025-01-29T10:30:00.000Z"
}
```

## 🔄 Migration Path

### For Existing Deployments

1. **Phase 1**: Deploy regional infrastructure
2. **Phase 2**: Migrate existing users to appropriate regions
3. **Phase 3**: Update frontend for regional support
4. **Phase 4**: Enable regional enforcement
5. **Phase 5**: Optimize and monitor

### For New Deployments

1. Start with single region
2. Add regions as needed
3. Use regional templates for consistency

---

## 📝 Version History

| Version | Date | Changes |
|---------|------|---------|
| 1.0 | 2025-01-29 | Initial multi-regional architecture |

---

**Last Updated**: 2025-01-29  
**Maintained By**: AmalPay Development Team  
**License**: Internal Use Only
