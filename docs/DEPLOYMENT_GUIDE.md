# AmalPay Multi-Regional Deployment Guide

## Prerequisites

### Infrastructure Requirements
- AWS Account with appropriate permissions
- MongoDB Atlas account (or self-hosted MongoDB clusters)
- Domain management access (for subdomains)
- SSL certificate management
- CI/CD pipeline access

### Technical Requirements
- Node.js 16+ 
- MongoDB 5.0+
- Docker (optional)
- AWS CLI configured
- Git access to repository

## Step-by-Step Deployment

### Phase 1: Database Setup

#### 1.1 Create Regional MongoDB Databases

**For MongoDB Atlas:**
```bash
# Create new databases in existing cluster
# Or create separate clusters per region for better isolation

Databases to create:
- amal_pay_texas
- amal_pay_california  
- amal_pay_canada
- amal_pay_florida
```

**Connection Strings:**
```
Texas: mongodb+srv://user:<EMAIL>/amal_pay_texas
California: mongodb+srv://user:<EMAIL>/amal_pay_california
Canada: mongodb+srv://user:<EMAIL>/amal_pay_canada
```

#### 1.2 Database Seeding

```bash
# Seed each regional database
NODE_ENV=texas npm run seed
NODE_ENV=california npm run seed
NODE_ENV=canada npm run seed
```

### Phase 2: Environment Configuration

#### 2.1 Create Regional Environment Files

**Texas Environment (`.env.texas`):**
```env
PROJECT_NAME="AmalPay Texas"
PORT=8400
REGION="texas"
REGION_CODE="TX"
REGION_NAME="Texas"

WEB_URL="https://texas.amalpay.com"
JWT_SECRET=313d8b0eaab1e837d2829109aec83b506968cd04b9be41da9396e0e2f373e3b3

MONGODB_URL=mongodb+srv://AmalBackend:<EMAIL>/
DB_NAME=amal_pay_texas

ENVIRONMENT=production

# Regional Restrictions
ALLOWED_STATES=["Texas"]
ALLOWED_COUNTRIES=["United States"]

# Regional Email
EMAIL_ADDRESS="<EMAIL>"
```

**California Environment (`.env.california`):**
```env
PROJECT_NAME="AmalPay California"
PORT=8400
REGION="california"
REGION_CODE="CA"
REGION_NAME="California"

WEB_URL="https://california.amalpay.com"
JWT_SECRET=313d8b0eaab1e837d2829109aec83b506968cd04b9be41da9396e0e2f373e3b3

MONGODB_URL=mongodb+srv://AmalBackend:<EMAIL>/
DB_NAME=amal_pay_california

ENVIRONMENT=production

# Regional Restrictions
ALLOWED_STATES=["California"]
ALLOWED_COUNTRIES=["United States"]

# Regional Email
EMAIL_ADDRESS="<EMAIL>"
```

#### 2.2 Update Package.json Scripts

```json
{
  "scripts": {
    "start": "node server.js",
    "start:texas": "NODE_ENV=texas node server.js",
    "start:california": "NODE_ENV=california node server.js",
    "start:canada": "NODE_ENV=canada node server.js",
    "seed": "node ./Database/Seeders/index.js",
    "seed:texas": "NODE_ENV=texas node ./Database/Seeders/index.js",
    "seed:california": "NODE_ENV=california node ./Database/Seeders/index.js"
  }
}
```

### Phase 3: AWS Infrastructure Setup

#### 3.1 EC2 Instances per Region

**Instance Configuration:**
```yaml
Instance Type: t3.medium (minimum)
OS: Ubuntu 20.04 LTS
Security Groups:
  - HTTP (80)
  - HTTPS (443)
  - SSH (22)
  - Custom TCP (8400)
```

**Regional Instances:**
```
texas-amalpay-backend (us-east-1)
california-amalpay-backend (us-west-1)  
canada-amalpay-backend (ca-central-1)
```

#### 3.2 Load Balancer Configuration

**Application Load Balancer per Region:**
```yaml
Texas ALB:
  - Domain: texas.amalpay.com
  - Target: texas-amalpay-backend:8400
  - Health Check: /api/health

California ALB:
  - Domain: california.amalpay.com
  - Target: california-amalpay-backend:8400
  - Health Check: /api/health
```

#### 3.3 SSL Certificates

**AWS Certificate Manager:**
```bash
# Request certificates for each subdomain
aws acm request-certificate \
  --domain-name texas.amalpay.com \
  --validation-method DNS

aws acm request-certificate \
  --domain-name california.amalpay.com \
  --validation-method DNS
```

### Phase 4: DNS Configuration

#### 4.1 Subdomain Setup

**DNS Records (Route 53 or your DNS provider):**
```
texas.amalpay.com     -> CNAME -> texas-alb.us-east-1.elb.amazonaws.com
california.amalpay.com -> CNAME -> california-alb.us-west-1.elb.amazonaws.com
canada.amalpay.com    -> CNAME -> canada-alb.ca-central-1.elb.amazonaws.com
```

#### 4.2 Health Check Configuration

```javascript
// Add to Routes/index.js
router.get("/health", (req, res) => {
    res.status(200).json({
        status: "healthy",
        region: process.env.REGION,
        timestamp: new Date().toISOString(),
        database: "connected" // Add DB health check
    });
});
```

### Phase 5: Application Deployment

#### 5.1 Server Configuration Updates

**Update server.js to load regional environment:**
```javascript
// At the top of server.js
const region = process.env.NODE_ENV || 'production';
require("dotenv").config({ path: `.env.${region}` });
```

#### 5.2 Deployment Scripts

**Deploy Script (`scripts/deploy.sh`):**
```bash
#!/bin/bash

REGION=$1
if [ -z "$REGION" ]; then
    echo "Usage: ./deploy.sh <region>"
    exit 1
fi

echo "Deploying to $REGION region..."

# Build application
npm install --production

# Copy regional environment
cp .env.$REGION .env

# Restart application with PM2
pm2 restart amalpay-$REGION || pm2 start server.js --name amalpay-$REGION

echo "Deployment to $REGION completed!"
```

#### 5.3 PM2 Configuration

**PM2 Ecosystem File (`ecosystem.config.js`):**
```javascript
module.exports = {
  apps: [
    {
      name: 'amalpay-texas',
      script: 'server.js',
      env: {
        NODE_ENV: 'texas'
      },
      instances: 2,
      exec_mode: 'cluster'
    },
    {
      name: 'amalpay-california',
      script: 'server.js',
      env: {
        NODE_ENV: 'california'
      },
      instances: 2,
      exec_mode: 'cluster'
    }
  ]
};
```

### Phase 6: Data Migration

#### 6.1 Existing User Migration

**Migration Script (`scripts/migrate-users.js`):**
```javascript
const mongoose = require('mongoose');
const UserModel = require('../Models/UserModel');

const regionMappings = {
    'Texas': 'texas',
    'California': 'california',
    'Ontario': 'canada',
    'Quebec': 'canada'
};

async function migrateUsers() {
    // Connect to original database
    await mongoose.connect(process.env.MONGODB_URL_ORIGINAL);
    
    const users = await UserModel.find({});
    
    for (const user of users) {
        const region = regionMappings[user.state] || 'default';
        
        // Connect to regional database
        const regionalConnection = await mongoose.createConnection(
            getRegionalConnectionString(region)
        );
        
        // Create user in regional database
        const RegionalUser = regionalConnection.model('User', UserSchema);
        await RegionalUser.create({
            ...user.toObject(),
            region: region,
            regionCode: getRegionCode(region)
        });
        
        console.log(`Migrated user ${user.email} to ${region}`);
    }
}
```

#### 6.2 Data Validation

```javascript
// Validate migration
async function validateMigration() {
    const regions = ['texas', 'california', 'canada'];
    
    for (const region of regions) {
        const connection = await connectToRegion(region);
        const count = await connection.model('User').countDocuments();
        console.log(`${region}: ${count} users`);
    }
}
```

### Phase 7: Testing Deployment

#### 7.1 Health Checks

```bash
# Test each regional endpoint
curl https://texas.amalpay.com/api/health
curl https://california.amalpay.com/api/health
curl https://canada.amalpay.com/api/health
```

#### 7.2 Authentication Testing

```bash
# Test regional authentication
curl -X POST https://texas.amalpay.com/api/auth/signIn \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password"}'
```

#### 7.3 Cross-Region Access Testing

```bash
# Should fail - Texas user accessing California endpoint
curl -X GET https://california.amalpay.com/api/user/profile \
  -H "Authorization: Bearer <texas-user-token>"
```

### Phase 8: Monitoring Setup

#### 8.1 CloudWatch Configuration

```yaml
Metrics to Monitor:
  - API Response Times per Region
  - Database Connection Health per Region
  - Error Rates per Region
  - User Authentication Success/Failure per Region
```

#### 8.2 Alerting

```yaml
Alerts:
  - Regional Database Disconnection
  - High Error Rate in Regional API
  - Cross-Region Access Attempts
  - Regional Service Downtime
```

### Phase 9: Frontend Updates

#### 9.1 Region Detection

```javascript
// Frontend region detection
const detectRegion = (userLocation) => {
    const regionMap = {
        'TX': 'texas',
        'CA': 'california',
        'ON': 'canada',
        'QC': 'canada'
    };
    
    return regionMap[userLocation.state] || 'default';
};
```

#### 9.2 Dynamic API Configuration

```javascript
// API client configuration
const getApiClient = (region) => {
    const baseURLs = {
        'texas': 'https://texas.amalpay.com/api',
        'california': 'https://california.amalpay.com/api',
        'canada': 'https://canada.amalpay.com/api',
        'default': 'https://amalpay.com/api'
    };
    
    return axios.create({
        baseURL: baseURLs[region] || baseURLs.default
    });
};
```

## Rollback Strategy

### Emergency Rollback

```bash
# Rollback to previous version
pm2 stop amalpay-texas
git checkout previous-stable-tag
npm install
pm2 start amalpay-texas
```

### Database Rollback

```bash
# Restore from backup
mongorestore --uri="mongodb+srv://..." --db=amal_pay_texas backup/
```

## Maintenance

### Regular Tasks

1. **Database Backups**: Daily automated backups per region
2. **Log Rotation**: Regional log management
3. **Security Updates**: Coordinated updates across regions
4. **Performance Monitoring**: Regional performance metrics

### Scaling

```bash
# Scale regional instances
pm2 scale amalpay-texas +2
pm2 scale amalpay-california +2
```

---

*Last Updated: 2025-01-29*
*Version: 1.0*
