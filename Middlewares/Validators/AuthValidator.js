const { query, body } = require("express-validator");

const {
	firstNameBodyValidator,
	lastNameBodyValidator,
	passwordBodyValidator
} = require("./CommonValidator")

const {
	DEVICE_TYPE,
	LOGIN_WITH
} = require("../../Configs/constants");

const emailBodyValidator = [
	body("email", "email required")
		.trim()
		.notEmpty()
		.bail()
		.isEmail()
		.withMessage("email is invalid"),
]

const emailQueryValidator = [
	query("email", "email required")
		.trim()
		.notEmpty()
		.bail()
		.isEmail()
		.withMessage("email is invalid"),
]

const otpBodyValidator = [
	body("otp", "OTP is required")
		.trim()
		.notEmpty()
]

const tokenBodyValidator = [
	body("token", "token is required")
		.trim()
		.notEmpty()
]

const tokenQueryValidator = [
	query("token", "token is required")
		.trim()
		.notEmpty()
]

const authValidators = [
	body("deviceId", "deviceId is required")
		.trim()
		.notEmpty(),

	body("timezone", "timezone is required")
		.trim()
		.notEmpty(),

	body("appVersion", "appVersion is required")
		.trim()
		.notEmpty(),

	body("deviceType", "Please provide deviceType")
		.trim()
		.isIn(Object.keys(DEVICE_TYPE))
		.withMessage(
			`Please provide deviceType within ${Object.keys(DEVICE_TYPE)}`
		),
]

exports.signIn = [
	...authValidators,
	...emailBodyValidator,
	...passwordBodyValidator,
];

exports.socialSignIn = [
	...authValidators,

	body("token", "token is required")
		.trim()
		.notEmpty(),

	body("loginWith", "Please provide loginWith")
		.trim()
		.isIn(Object.keys(LOGIN_WITH))
		.withMessage(
			`Please provide loginWith within ${Object.keys(LOGIN_WITH)}`
		),
]

exports.signUp = [
	...authValidators,
	...firstNameBodyValidator,
	...lastNameBodyValidator,
	...emailBodyValidator,
	// Conditionally add passwordBodyValidator
	(req, res, next) => {
		if (!req.body.appleSignIn) {
			return passwordBodyValidator[0](req, res, next);
		}
		next();
	}	
];

exports.resentOTP = [
	...emailQueryValidator,
]

exports.verifyOTP = [
	...emailBodyValidator,
	...otpBodyValidator,
];

exports.forgotPassword = [
	...emailBodyValidator,
]

exports.verifyForgotPassword = [
	...tokenQueryValidator,
]

exports.resetPassword = [
	...passwordBodyValidator,
	...tokenBodyValidator,
]
