const { query, body, param } = require("express-validator");

const { searchKeyValidator } = require("./CommonValidator");

exports.listFaqs = [...searchKeyValidator];

exports.addFaqs = [
  body("question", "Question is required")
    .isString()
    .withMessage("Question is must be string")
    .bail()
    .trim()
    .isLength({ min: 4 })
    .withMessage("Question is at least 4 characters long"),

  body("answer", "Answer is required")
    .isString()
    .withMessage("Answer is must be string")
    .bail()
    .trim()
    .isLength({ min: 2 })
    .withMessage("Answer is at least 2 characters long"),

  body("topic").optional().isArray().withMessage("Topic must be an array"),
];

exports.updateFaqs = [
  param("id", "Id is required").isMongoId().withMessage("Id must be valid id"),
  body("question", "Question is required")
    .optional()
    .isString()
    .withMessage("Question is must be string")
    .bail()
    .trim()
    .isLength({ min: 4 })
    .withMessage("Question is at least 4 characters long"),

  body("answer", "Answer is required")
    .optional()
    .isString()
    .withMessage("Answer is must be string")
    .bail()
    .trim()
    .isLength({ min: 2 })
    .withMessage("Answer is at least 2 characters long"),

  body("topic").optional().isArray().withMessage("Topic must be an array"),
];

exports.deleteFaqs = [
  param("id", "Id is required").isMongoId().withMessage("Id must be valid id"),
];
