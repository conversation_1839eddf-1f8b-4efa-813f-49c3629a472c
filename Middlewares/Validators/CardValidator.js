const { query, body } = require("express-validator");

const {
    search<PERSON>eyValidator,
    headerAuthValidator,
} = require("./CommonValidator");

exports.createCard = [
    ...headerAuthValidator,

    body("name", "name is required")
        .trim()
        .notEmpty(),

    body("number", "number is required")
        .trim()
        .notEmpty(),

    body("cvv", "cvv is required")
        .trim()
        .notEmpty(),

    body("expire", "expire is required")
        .trim()
        .notEmpty(),
];

exports.listCard = [
    ...headerAuthValidator,
    ...searchKeyValidator
]

exports.deleteCard = [
    ...headerAuthValidator,

    query("cardId", "cardId is required")
        .trim()
        .notEmpty()
        .isMongoId(),
]
