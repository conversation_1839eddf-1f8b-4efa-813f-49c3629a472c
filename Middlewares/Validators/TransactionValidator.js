const { query, body } = require("express-validator");
const {
  paginationValidator,
  basicFilterValidator,
} = require("./CommonValidator");
const { PAYMENT_STATUS_TYPE } = require("../../Configs/constants");

exports.transactionValidator = [
  ...paginationValidator,
  query("status").optional().isString().trim(),
  query("state")
    .optional()
    .isString()
    .withMessage("Status must be string")
    .trim(),
];

exports.transactionListingValidator = [
  ...basicFilterValidator,
  query("collectionMode")
    .optional()
    .isString()
    .withMessage("Collection mode must be string")
    .trim(),
  query("payoutMethod")
    .optional()
    .isString()
    .withMessage("Payout method must be string")
    .trim(),
  query("paymentStatus")
    .optional()
    .isString()
    .withMessage("Payment status must be string")
    .toUpperCase()
    .isIn(Object.values(PAYMENT_STATUS_TYPE)),
  query("country")
    .optional()
    .isInt()
    .withMessage("Invalid country value it must be integer"),
  query("searchValue")
    .optional()
    .isString()
    .withMessage("Search value must be string"),
  query("recipientId").optional().isMongoId().withMessage("Invalid mongodb id"),
  query("userId").optional().isMongoId().withMessage("Invalid mongodb id"),
];

exports.transactionLimitValidator = [
  query("transactionLimitId", "Transaction limit id is required")
    .isMongoId()
    .withMessage("Invalid id"),

  body("minLimit", "Minimum Limit is required")
    .isInt({ min: 1 })
    .withMessage("Minimum limit must be integer and minimum value 1"),

  body("maxLimit", "Maximum Limit is required")
    .isInt({ min: 1 })
    .withMessage("Maximum limit must be integer and minimum value 1")
    .bail()
    .custom((value, { req }) => {
      if (!req.body.minLimit) {
        throw new Error("Maximum limit can't exists without minimum limit");
      }
      if (value < req.body.minLimit) {
        throw new Error("Maximum limit must be greater than minimum limit");
      }

      return true;
    }),
];
