const { body, query } = require("express-validator");
const { headerAuthValidator } = require("./CommonValidator");

// Reusable Validators
const requiredField = (fieldName, errorMessage) =>
    body(fieldName, errorMessage).trim().notEmpty();

const optionalMongoId = (fieldName, errorMessage) =>
    body(fieldName, errorMessage).optional({ checkFalsy: true }).trim().isMongoId();

const requiredMongoId = (fieldName, errorMessage) =>
    body(fieldName, errorMessage).trim().notEmpty().isMongoId();

// Transaction Validator
const transactionValidator = [
    requiredField("transactionId", "transactionId is required"),
];

exports.countryQueryValidator = [
    query("countryId", "countryId is required")
        .default(process.env.MEGA_TECH_COUNTRY_ID)
        .trim()
        .notEmpty(),
]

exports.validStateCheckValidator = [
    body("stateName", "stateName is required")
        .trim()
        .notEmpty()
]

// Service Validators
const createPaymentValidator = [
    requiredField("serviceId", "serviceId is required"),
    requiredField("serviceOperatorId", "serviceOperatorId is required"),
    requiredField("payoutMethod", "payoutMethod is required"),
    requiredField("amount", "amount is required"),
    requiredField("purpose", "purpose is required"),
    requiredField("cvv", "cvv is required"),
    requiredField("sourceCountryName", "source country name is required"),
    requiredField("sourceCountryId", "source country id is required"),
    requiredField("sourceCityId", "source city id is required"),
    requiredField("paymentCityName", "payment city name is required"),
    requiredField("sourceCityName", "source city name is required"),
    requiredField("sourceOfIncome", "Source of income is required"),
    optionalMongoId("cardId", "cardId must be a valid MongoDB ID"),
    requiredMongoId("recipientId", "recipientId must be a valid MongoDB ID"),
];

// Exports
exports.listServices = [
    ...headerAuthValidator,
    ...this.countryQueryValidator,

    query("includeOperators", "includeOperators is required")
        .default(true)
        .isBoolean()
        .toBoolean()
];

exports.checkRecipient = [
    ...headerAuthValidator,

    query("serviceId", "serviceId is required")
        .trim()
        .notEmpty(),
    query("serviceOperatorId", "serviceOperatorId is required")
        .trim()
        .notEmpty(),
    query("recipientId", "recipientId is required")
        .trim()
        .notEmpty()
        .isMongoId(),
    query("checkAccount", "checkAccount is required")
        .default(false)
        .isBoolean()
        .toBoolean()
];

exports.listPricing = [
    ...this.countryQueryValidator,
    query("amount", "amount is required")
        .trim()
        .notEmpty()
];

exports.createPayment = [
    ...headerAuthValidator,
    ...createPaymentValidator,

    body("destinationCountryId", "destinationCountryId is required")
        .trim()
        .notEmpty(),
    body("destinationCurrency", "destinationCurrency is required")
        .trim()
        .notEmpty(),
    body("stateName", "stateName is required")
        .trim()
        .notEmpty()
];

exports.trackTransaction = [
    ...headerAuthValidator,
    ...transactionValidator,
];

exports.stateIdValidator = [
    query("stateId", "stateId is required")
        .trim()
        .notEmpty()
]

exports.exchangeRateValidator = [
    body("countryId", "countryId is required")
        .trim()
        .notEmpty(),
    body("currency", "currency is required")
        .trim()
        .notEmpty(),
]
