exports.csvFileValidator = (req, res, next) => {
  if (req.file?.mimetype !== "text/csv")
    return res.handler.badRequest("VALIDATION.FILE.INVAlID_CSV_FILE");
  next();
};

exports.imageFileValidator = (req, res, next) => {
  if (req?.files?.profilePicture) {
    if (
      req.files?.profilePicture?.[0]?.mimetype !== "image/png" &&
      req.files?.profilePicture?.[0]?.mimetype !== "image/jpg" &&
      req.files?.profilePicture?.[0]?.mimetype !== "image/jpeg"
    ) {
      return res.handler.badRequest("VALIDATION.FILE.INVALID_IMAGE_FILE");
    }

    if (req.files?.profilePicture?.[0]?.size >= 4194304) {
      return res.handler.badRequest("VALIDATION.FILE.INVALID_IMAGE_SIZE");
    }
  }
  next();
};
