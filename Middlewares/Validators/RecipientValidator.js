const { query, body } = require("express-validator");

const {
    search<PERSON>eyValidator,
    headerAuthValidator
} = require("./CommonValidator");

const recipientInfo = [
    body("email", "email is required")
        .optional()
        .trim()
        .isEmail()
        .withMessage("email must be a valid email address"),

    body("address", "address is required")
        .trim()
        .notEmpty(),

    body("country", "country is required")
        .trim()
        .notEmpty(),

    body("countryId", "country is required")
        .trim()
        .notEmpty(),

    body("state", "state is required")
        .trim()
        .notEmpty(),

    body("city", "city is required")
        .trim()
        .notEmpty(),

    body("cityId", "cityId is required")
        .trim()
        .notEmpty(),

    body("zipCode", "zipCode is required")
        .trim()
        .notEmpty(),

    body("countryCode", "countryCode is required")
        .trim()
        .notEmpty(),

    body("phoneNumber", "phoneNumber is required")
        .trim()
        .notEmpty(),

    body("firstName", "name is required")
        .trim()
        .notEmpty(),

    body("middleName", "name is required")
        .optional()
        .trim()
        .notEmpty(),

    body("lastName", "name is required")
        .trim()
        .notEmpty(),

    body("bankName", "address is required")
        .optional()
        .trim()
        .notEmpty(),

    body("accountNumber", "accountNumber is required")
        .optional()
        .trim()
        .notEmpty(),

    body("billingAddress", "billing address is required")
        .trim()
        .notEmpty(),

    body("billingState", "billing State is required")
        .trim()
        .notEmpty(),

    body("billingCity", "billing City is required")
        .trim()
        .notEmpty(),

    body("billingZipCode", "billing State is required")
        .optional()
        .trim(),
]

const recipientIdQueryValidator = [
    query("recipientId", "recipientId is required")
        .trim()
        .notEmpty()
        .isMongoId(),
]

exports.createRecipient = [
    ...headerAuthValidator,
    ...recipientInfo,
];

exports.updateRecipient = [
    ...headerAuthValidator,
    ...recipientIdQueryValidator,
    ...recipientInfo,
];

exports.listRecipient = [
    ...headerAuthValidator,
    ...searchKeyValidator
]

exports.deleteRecipient = [
    ...headerAuthValidator,
    ...recipientIdQueryValidator,
]
