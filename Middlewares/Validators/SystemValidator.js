const { body, query } = require('express-validator')
const { headerAuthValidator } = require('./CommonValidator')

const slugQueryValidator = [
    query("slug", "slug is required")
        .trim()
        .notEmpty()
]

exports.findSystemValue = [
    ...headerAuthValidator,
    ...slugQueryValidator
]

exports.updateSystemValue = [
    ...headerAuthValidator,

    body("slug", "slug is required")
        .trim()
        .notEmpty(),

    body("title", "title is required")
        .trim()
        .notEmpty(),

    body("description", "description is required")
        .optional()
        .trim()
        .notEmpty(),

    body("value", "value is required")
        .exists()
]

exports.listPages = [
    ...headerAuthValidator,
]

exports.getPageDetail = [
    ...headerAuthValidator,
    ...slugQueryValidator
]

exports.updatePage = [
    ...headerAuthValidator,
    ...slugQueryValidator,

    body("title", "title is required")
        .trim()
        .notEmpty(),

    body("content", "content is required")
        .trim()
        .notEmpty(),
]

exports.listTemplates = [
    ...headerAuthValidator,
]

exports.getTemplateDetail = [
    ...headerAuthValidator,
    ...slugQueryValidator
]

exports.updateTemplate = [
    ...headerAuthValidator,
    ...slugQueryValidator,

    body("title", "title is required")
        .trim()
        .notEmpty(),

    body("content", "content is required")
        .trim()
        .notEmpty(),
]
