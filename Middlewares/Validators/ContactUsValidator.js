const { body } = require("express-validator");
const {
  firstNameBodyValidator,
  lastNameBodyValidator,
} = require("./CommonValidator");

exports.validateContactUs = [
  ...firstNameBodyValidator,
  ...lastNameBodyValidator,
  body("email", "email required")
    .trim()
    .notEmpty()
    .bail()
    .isEmail()
    .withMessage("email is invalid"),
  body("message", "Message is required").trim(),
  body("phoneNumber", "phoneNumber is required").trim().notEmpty(),
  body("countryCode", "countryCode is required").trim().notEmpty(),
  body("privacyPolicy").optional().isBoolean(),
];
