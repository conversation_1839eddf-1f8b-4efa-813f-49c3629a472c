const { query, body, param } = require("express-validator");
const { basicFilterValidator } = require("./CommonValidator");

exports.userListValidator = [
  ...basicFilterValidator,

  query("country").optional().isInt().withMessage("Country must be integer"),
  query("city").optional().isString().withMessage("City must be string"),
  query("state")
    .optional()
    .trim()
    .isString()
    .withMessage("State must be string"),
  query("userSource")
    .optional()
    .trim()
    .isString()
    .withMessage("User source must be string"),
  query("filterValue")
    .optional()
    .trim()
    .isString()
    .withMessage("Filter value must be string"),
];

const addUserValidator = [
  body("firstName", "First Name is required").isString(
    "First name must be string"
  ),
  body("lastName", "Last Name is required").isString(
    "Last name must be string"
  ),
  body("email", "Email id is required")
    .isEmail()
    .withMessage("Invalid email id"),

  body("countryCode", "Country code is required")
    .isString()
    .withMessage("Country code must be string")
    .trim()
    .isLength({ min: 2, max: 4 })
    .withMessage(
      "country code must have minimum 2 characters and maximum 4 characters"
    ),

  body("phoneNumber", "Phone number is required")
    .trim()
    .isString()
    .withMessage("Phone number must be string")
    .isMobilePhone()
    .withMessage("Invalid mobile number"),
  body("state", "State is required")
    .isString()
    .withMessage("State must be string")
    .trim(),

  body("city", "City is required")
    .isString()
    .withMessage("city must be string")
    .trim(),

  body("birthDate", "Birth date is required")
    .isDate()
    .withMessage("Birth date is must be valid date"),

  body("zipCode", "Zip code is required")
    .isString()
    .trim()
    .isLength({ min: 4 })
    .custom((value) => {
      const zipCodeRegX = /^[A-Za-z0-9\- ]*$/;
      if (!zipCodeRegX.test(value)) throw new Error("Invalid zip code");
      return true;
    }),

  body("country", "Country is required")
    .isString()
    .withMessage("Country is must be string"),

  body("nationality", "Nationality is required")
    .isString()
    .withMessage("Nationality is must be string"),

  body("nationalId", "National id is required")
    .isString()
    .withMessage("Nationality is must be string"),

  body("gender", "Gender is required")
    .isIn(["MALE", "FEMALE"])
    .withMessage("Invalid enum value it must be string with uppercase"),
];

const addDocumentValidator = [
  body("documentNumber", "Document number is required"),
  body("documentType", "Document Type is required")
    .isIn(["passport", "drivingLicense", "nationalId"])
    .withMessage("Invalid document type"),

  body("documentIssueDate")
    .optional()
    .isDate()
    .withMessage("Document issue date must be valid date")
    .bail()
    .custom((value, { req }) => {
      if (!req.body.documentExpireDate) {
        throw new Error(
          "Document issue date and Document expire date must be exists together"
        );
      }
      return true;
    }),

  body("documentExpireDate")
    .optional()
    .isDate()
    .withMessage("End date must be valid date")
    .bail()
    .custom((value, { req }) => {
      if (!req.body.documentIssueDate && value) {
        throw new Error(
          "Document issue date and Document expire date must be exists together"
        );
      }
      const startDate = req.body.documentIssueDate
        ? new Date(req.body.documentIssueDate).getTime()
        : null;
      const endDate = new Date(value).getTime();

      if (startDate > endDate) {
        throw new Error(
          "Document issuer date must be before then document end expired date"
        );
      }
      return true;
    }),
];

exports.addUserFromAdmin = [...addUserValidator, ...addDocumentValidator];


exports.bodyMongoIdValidator = [
  body("userId", "Id is required").isMongoId().withMessage("Invalid id"),
];

exports.passwordBodyValidator = [
  body("password", "Password must contain at least 8 characters, including one uppercase letter, one lowercase letter, and one number")
      .exists()
      .withMessage("Password is required")
      .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/)
      .withMessage("Password must contain at least 8 characters, including one uppercase letter, one lowercase letter, and one number"),
];

exports.paramsMongoIdValidator = [
  param("userId").isMongoId().withMessage("Invalid id"),
];

exports.addUserValidator = addUserValidator;
exports.addDocumentValidator = addDocumentValidator;
