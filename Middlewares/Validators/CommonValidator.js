const { header, body, query } = require('express-validator')

exports.headerAuthValidator = [
    header("authorization", "Please provide authorization.")
        .trim()
        .notEmpty(),
]

exports.firstNameBodyValidator = [
    body("firstName", "firstName is required")
        .trim()
        .notEmpty()
];

exports.lastNameBodyValidator = [
    body("lastName", "lastName is required")
        .trim()
        .notEmpty()
];

exports.passwordBodyValidator = [
    body("password", "Password must contain at least 8 characters, including one uppercase letter, one lowercase letter, and one number")
        .exists()
        .withMessage("Password is required")
        .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d]{8,}$/)
        .withMessage("Password must contain at least 8 characters, including one uppercase letter, one lowercase letter, and one number"),
];

exports.searchKeyValidator = [
    query("searchKey")
        .trim()
        .optional()
        .isLength({ min: 1 })
        .withMessage("Minimum search value must be 1 characters long."),
];

exports.projectionsQueryValidator = [
    query("projections", "Please provide as least one value in projections")
        .optional()
        .isArray({ min: 1 })
        .bail()
        .customSanitizer(v => v.join(" ")),

    query("projections.*", "Please provide valid projections")
        .trim()
        .notEmpty(),
]

exports.paginationValidator = [
    query("limit").optional().isNumeric("Page number must be a number"),
    query("pageNumber").optional().isNumeric("Page number must be a number"),
]


exports.basicFilterValidator = [
    query("limit").optional().isInt().withMessage("Limit must be integer"),
    query("page").optional().isInt().withMessage("Page must be integer"),
  
    query("startDate")
      .optional()
      .isDate()
      .withMessage("Start date must be valid date"),
      
    query("endDate")
      .optional()
      .isDate()
      .withMessage("End date must be valid date")
      .bail()
      .custom((value, { req }) => {
        if (!req.query.startDate && value) {
          throw new Error("Start and end date must be exists together");
        }
        const startDate = req.query.startDate
          ? new Date(req.query.startDate).getTime()
          : null;
        const endDate = new Date(value).getTime();
  
        if (startDate > endDate) {
          throw new Error("Start date must be before then end date");
        }
        return true;
      }),
  
    query("filterValue")
      .optional()
      .isString()
      .withMessage("Filter value must be string"),
  ];