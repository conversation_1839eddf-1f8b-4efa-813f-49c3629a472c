const { query, body } = require("express-validator");

const {
	firstNameBodyValidator,
	lastNameBodyValidator,
	passwordBodyValidator,
	headerAuthValidator,
	projectionsQueryValidator
} = require("./CommonValidator")

exports.getProfile = [
	...headerAuthValidator,
	...projectionsQueryValidator
]

exports.updateProfile = [
	...headerAuthValidator,
]

exports.changePassword = [
	...headerAuthValidator,
	...passwordBodyValidator,

	body("oldPassword", "old password required")
		.trim()
		.notEmpty(),
]
