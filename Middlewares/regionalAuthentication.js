const { dbManager } = require("../Configs/database");
const UserModel = require("../Models/UserModel");

class RegionalAuthentication {
    
    /**
     * Middleware to validate user belongs to current region
     */
    static async validateRegion(req, res, next) {
        try {
            const currentRegion = process.env.REGION || 'default';
            const userRegion = req.user?.region;
            
            if (!userRegion) {
                return res.handler.unauthorized("User region not specified");
            }
            
            if (userRegion !== currentRegion) {
                return res.handler.unauthorized(`Access denied. User belongs to ${userRegion} region, but accessing ${currentRegion} region.`);
            }
            
            next();
        } catch (err) {
            res.handler.serverError(err);
        }
    }
    
    /**
     * Middleware to set regional database connection
     */
    static async setRegionalConnection(req, res, next) {
        try {
            const region = req.headers['x-region'] || req.query.region || process.env.REGION || 'default';
            
            // Get regional connection
            const connection = dbManager.getConnection(region);
            
            if (!connection) {
                return res.handler.badRequest(`Invalid region: ${region}`);
            }
            
            // Attach regional connection to request
            req.dbConnection = connection;
            req.region = region;
            
            next();
        } catch (err) {
            res.handler.serverError(err);
        }
    }
    
    /**
     * Enhanced authentication that includes region validation
     */
    static async authenticateWithRegion(userTypes = []) {
        return async (req, res, next) => {
            try {
                // First set regional connection
                await RegionalAuthentication.setRegionalConnection(req, res, () => {});
                
                // Then perform standard authentication
                const Authentication = require("./authentication");
                await Authentication.all(req, res, () => {}, userTypes);
                
                // Finally validate region
                await RegionalAuthentication.validateRegion(req, res, next);
                
            } catch (err) {
                res.handler.serverError(err);
            }
        };
    }
    
    /**
     * Get allowed regions for current deployment
     */
    static getAllowedRegions() {
        return {
            region: process.env.REGION || 'default',
            regionCode: process.env.REGION_CODE || 'DEFAULT',
            regionName: process.env.REGION_NAME || 'Default Region',
            allowedStates: process.env.ALLOWED_STATES ? JSON.parse(process.env.ALLOWED_STATES) : [],
            allowedCountries: process.env.ALLOWED_COUNTRIES ? JSON.parse(process.env.ALLOWED_COUNTRIES) : []
        };
    }
    
    /**
     * Validate if user's location is allowed in current region
     */
    static validateUserLocation(userState, userCountry) {
        const { allowedStates, allowedCountries } = RegionalAuthentication.getAllowedRegions();
        
        const stateAllowed = allowedStates.length === 0 || allowedStates.includes(userState);
        const countryAllowed = allowedCountries.length === 0 || allowedCountries.includes(userCountry);
        
        return stateAllowed && countryAllowed;
    }
}

module.exports = RegionalAuthentication;
