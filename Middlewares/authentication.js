const { validationResult } = require('express-validator');
const { USER_TYPE } = require("../Configs/constants");

const UserModel = new (require("../Models/UserModel"))()
const ModulePermission = new (require("../Models/ModulePermission"))()

module.exports = class Authentication {

	static async all(req, res, next, userTypes = [USER_TYPE.USER, USER_TYPE.ADMIN, USER_TYPE.SUB_ADMIN]) {
		try {
			//Check error if exist then send validationError
			const errors = validationResult(req)
			if (!errors.isEmpty()) {
				return res.handler.validationError(undefined, errors.array())
			}

			//If no Authentication required means userTypes = []
			if (userTypes.length == 0) return next()

			//Get & check authToken if not exist then send unauthorized

			if (!req.headers.authorization)
				return res.handler.unauthorized()

			const authToken = req.headers.authorization.split("Bearer ")[1]
			if (!authToken)
				return res.handler.unauthorized()

			req.headers.authToken = authToken

			// Get array of data for that user type
			let {
				user,
				session
			} = await UserModel.getDetailByAuthToken(
				authToken,
				{
					firstName: 1,
					lastName: 1,
					email: 1,
					isActive: 1
				},
				true
			);

			if (!user || !user.isActive)
				return res.handler.unauthorized();

			if (
				user.roleId.slug !== USER_TYPE.ADMIN &&
				user.roleId.slug !== USER_TYPE.USER &&
				user.roleId.canAccessCMS
			) {
				user.roleId.slug = USER_TYPE.SUB_ADMIN;
			}

			if (!userTypes.includes(user.roleId.slug)) {
				return res.handler.unauthorized();
			}

			req[user.roleId.slug] = user;
			req.userId = user._id
			req.session = session
			next();

		} catch (err) {
			res.handler.serverError(err)
		}
	}

	//For User Authentication
	static async user(...params) {
		await Authentication.all(...params, [USER_TYPE.USER])
	}

	static async admin(...params) {
		await Authentication.all(...params, [USER_TYPE.ADMIN, USER_TYPE.SUB_ADMIN]);
	}

	//For No Authentication
	static async blank(...params) {
		await Authentication.all(...params, []);
	}

	static async hasModuleAccess(req, res, next, module, actionType) {
		if (req.subAdmin || req.admin) {
			const permissionInfo = await ModulePermission.verifyPermission(module, req?.subAdmin?.roleId ?? req?.admin?.roleId);
			if (!permissionInfo || !permissionInfo[actionType])
				return res.handler.unauthorized();
			next();
		}
		else {
			return res.handler.unauthorized();
		}


	}

}