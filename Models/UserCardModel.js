const UserCardSchema = require("../Database/Schemas/UserCardSchema");

module.exports = class {

	createCard = (body, userId) => {
		return new UserCardSchema({
			...body,
			userId,
			createdBy: userId,
			updatedBy: userId
		}).save();
	};

	findCardLists = (filter, projection, option) => {
		return UserCardSchema.find(filter, projection, option);
	};

	findCardById = async (_id, projection) => {
		return UserCardSchema.findById(_id, projection)
	};

	listCardByUserId = (userId) => {
		return this.findCardLists(
			{
				userId,
				deletedAt: {
					$exists: false
				},
			},
			undefined,
			{
				lean: true,
				sort: {
					created_at: -1,
				},
			}
		);
	};

	deleteCard = (filter, userId) => {
		return UserCardSchema.updateOne(filter, {
			deletedAt: new Date(),
			updatedBy: userId
		})
	}

	deleteCardById = (cardId, userId) => {
		return this.deleteCard(
			{
				_id: cardId
			},
			userId
		)
	}

}