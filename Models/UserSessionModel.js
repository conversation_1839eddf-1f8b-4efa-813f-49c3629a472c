const UserSessionSchema = require("../Database/Schemas/UserSessionSchema")

const encrypt = new (require("../Configs/encrypt"))

module.exports = class {

    addSession = async (
        body,
        loginWith,
        userId
    ) => {
        await this.deleteSessions({
            deviceId: body.deviceId
        })

        return new UserSessionSchema({
            ...body,
            userId,
            loginWith,
            authToken: encrypt.generateAuthToken()
        }).save()
    }

    deleteSessions = async (filter) => {
        return await UserSessionSchema.deleteMany(filter)
    }

    findSession = (filter, projection, option) => {
        return UserSessionSchema.findOne(filter, projection, option)
    }

    findSessionByAuthToken = (authToken) => {
        return this.findSession(
            {
                authToken
            },
            "userId timezone",
            {
                lean: true
            }
        )
    }

}