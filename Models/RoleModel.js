const { default: mongoose } = require("mongoose");
const RoleSchema = require("../Database/Schemas/RoleSchema");

module.exports = class {
  findRoleByFilter = (filter, projection, option) => {
    return RoleSchema.findOne(
      filter,
      projection,
      option ?? {
        lean: true,
      }
    );
  };

  findRoleBySlug = (slug, projection, option) => {
    return this.findRoleByFilter(
      {
        slug,
        deletedAt: {
          $exists: false
        }
      },
      projection,
      option
    );
  };

  getAllRoles(filter = {}) {
    return RoleSchema.find(filter);
  }

  addNewRole(roleInfo) {
    return RoleSchema.create(roleInfo);
  }

  updateNewRoleById(roleId, updatedInfo) {
    return RoleSchema.findByIdAndUpdate(roleId, updatedInfo, { new: true });
  }
};
