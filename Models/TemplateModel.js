const TemplateSchema = require("../Database/Schemas/TemplateSchema")

const {
    replaceInString
} = require("../Helpers/GeneralHelpers");

module.exports = class {

    listTemplates = (filter, projection, option) => {
        return TemplateSchema.find(filter, projection, option)
    }

    findTemplate = (filter, projection, option) => {
        return TemplateSchema.findOne(filter, projection, option)
    }

    findTemplateBySlug = (slug, projection, option) => {
        return TemplateSchema.findOne({
            slug
        }, projection, option)
    }

    getUpdatedTemplate = async (slug, data) => {
        const template = await this.findTemplateBySlug(
            slug,
            undefined,
            {
                lean: true
            }
        )

        template.title = replaceInString(template.title, data)
        template.content = replaceInString(template.content, data)

        return template
    }

    updateTemplatesBySlug = (slug, body) => {
        return TemplateSchema.updateOne(
            {
                slug
            },
            body
        )
    }


}
