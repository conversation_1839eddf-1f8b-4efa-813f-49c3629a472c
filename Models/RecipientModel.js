const RecipientSchema = require("../Database/Schemas/RecipientSchema");

module.exports = class {

    createRecipient = (body, userId) => {
        return new RecipientSchema({
            ...body,
            userId,
            createdBy: userId,
            updatedBy: userId
        }).save();
    };

    findRecipientLists = (filter, projection, option) => {
        return RecipientSchema.find(filter, projection, option).sort({ createdAt: "desc" });
    };

    findRecipientById = (_id, projection, option) => {
        return RecipientSchema.findOne(
            {
                _id,
            },
            projection,
            option
        )
    }

    updateRecipient = (filter, updatedData) => {
        return RecipientSchema.updateOne(filter, updatedData);
    };

    listRecipientByUserId = (userId, body = {}) => {
        const filter = {
            userId,
            deletedAt: {
                $exists: false
            },
        }

        const {
            countryId,
            filterValue
        } = body

        if (countryId) {
            filter.countryId = countryId
        }

        if (filterValue) {
            filter.$or = [
                { firstName: { $regex: filterValue, $options: "i" } },
                { lastName: { $regex: filterValue, $options: "i" } },
                { email: { $regex: filterValue, $options: "i" } },
                { countryId: { $regex: filterValue, $options: "i" } },
            ];
        }

        return this.findRecipientLists(
            filter,
            undefined,
            {
                lean: true,
                sort: {
                    createdAt: -1,
                },
            }
        );
    };

    deleteRecipient = (filter, userId) => {
        return RecipientSchema.updateOne(filter, {
            deletedAt: new Date(),
            updatedBy: userId
        })
    }

    deleteRecipientById = (RecipientId, userId) => {
        return this.deleteRecipient(
            {
                _id: RecipientId
            },
            userId
        )
    }

}