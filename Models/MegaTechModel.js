const { default: axios } = require("axios")
const moment = require('moment-timezone');

const { DEFAULT_TIMEZONE } = require("../Configs/constants");

const VeriffDocumentSchema = require("../Database/Schemas/VeriffDocumentSchema");


module.exports = class MegaTechManager {

    static #token = ""

    #headers = () => {
        return {
            "Content-Type": "application/json",
            Authorization: "Bearer " + MegaTechManager.#token,
            Username: process.env.MEGA_TECH_USERNAME,
            Secretkey: process.env.MEGA_TECH_SECRET_KEY,
            Password: process.env.MEGA_TECH_PASSWORD,
        }
    }

    #callAPI = async (
        path,
        method,
        options = {},
    ) => {

        const data = options.data ?? {}
        data.Clientkey = process.env.MEGA_TECH_CLIENT_KEY
        
        options.data = data

        const config = {
            url: process.env.MEGA_TECH_URL + "/" + path,
            method,
            ...options
        }

        try {
            return await axios.request({
                ...config,
                headers: this.#headers()
            })
        } catch (error) {
            if (error.response.status == 401) {
                await this.#authenticate()

                try {
                    return await axios.request({
                        ...config,
                        headers: this.#headers()
                    })
                }
                catch (err) {
                    console.log(err.response.data);
                    throw new Error(JSON.stringify(err.response.data))
                }
            }
            else {
                console.log(error.response.data);
                throw new Error(error.response.data)
            }
        }
    }

    #authenticate = async () => {
        const res = await this.#callAPI(
            "Auth/Token",
            "get",
            {
                auth: {
                    username: process.env.MEGA_TECH_USERNAME,
                    password: process.env.MEGA_TECH_PASSWORD
                }
            }
        )

        const token = res.data.response.result.token
        MegaTechManager.#token = token
    }

    confirmTransaction = async (transactionId) => {
        const res = await this.#callAPI(
            "Services/ConfirmTransaction",
            "post",
            {
                data: {
                    TransactionRefNo: transactionId
                }
            }
        )

        return res.data.response.result
    }

    trackTransaction = async (transactionId) => {
        const res = await this.#callAPI(
            "Services/GetTransactionStatus",
            "post",
            {
                data: {
                    transactionNo: transactionId
                }
            }
        )

        return res.data.response.result
    }

    getDestinationCountries = async () => {
        const res = await this.#callAPI(
            "Services/GetDestinationCountries",
            "post"
        )

        const country = res.data.response.result.countries.country
        const countries = Array.isArray(country) ? country : [country]

        return countries
    }

    getCities = async (countryId) => {
        const res = await this.#callAPI(
            "Services/GetCitiesByCountryId",
            "post",
            {
                data: {
                    countryId
                }
            }
        )

        const city = res.data.response.result.Cities.City
        const cities = Array.isArray(city) ? city : [city]

        return cities
    }

    getExchangeRate = async ({ countryId, currency }) => {
        const res = await this.#callAPI(
            "Services/getExchangeRate",
            "post",
            {
                data: {
                    countryId,
                    currency
                }
            }
        )
        
        const result = res.data.response.result.ExchangeRates
        return result
    }

    getExchangeRateForPayment = async (body) => {
        const res = await this.#callAPI(
            "Services/getExchangeRate",
            "post",
            {
                data: {
                    countryId: body.destinationCountryId,
                    currency: body.destinationCurrency
                }
            }
        )
        const result = res.data.response.result.ExchangeRates;
        if (!result || !result.ExchangeRate) {
            return result
        }
        const exchangeRate = parseFloat(result.ExchangeRate);
        const convertedAmount = (body.amount * exchangeRate).toFixed(2);
        return {
            amountToReceive: parseFloat(convertedAmount),
            exchangeRate: exchangeRate.toFixed(2),
        };
    }

    getServices = async (countryId) => {
        const res = await this.#callAPI(
            "Services/getServices",
            "post",
            {
                data: {
                    countryId
                }
            }
        )

        const service = res.data.response.result.Service
        const services = Array.isArray(service) ? service : [service]

        return services
    }

    getServiceOperators = async (
        countryId,
        serviceId,
    ) => {
        const res = await this.#callAPI(
            "Services/getServiceOperatorsCurrency",
            "post",
            {
                data: {
                    countryId,
                    serviceId
                }
            }
        )

        const serviceOperator = res.data.response.result.ServiceOperator ?? []
        const serviceOperators = Array.isArray(serviceOperator) ? serviceOperator : [serviceOperator]

        return serviceOperators
    }

    verifyMobileNumber = async (data) => {             
        const res = await this.#callAPI(
            "Services/Verifymobilenumber",
            "post",
            {
                data: {
                    ...data,
                    sourceBranchkey: process.env.MEGA_TECH_SOURCE_BRANCH_KEY,
                }
            }
        )

        return res.data.response.result
    }

    verifyBankAccount = async (data) => {
        const res = await this.#callAPI(
            "Services/Verifybankaccount",
            "post",
            {
                data: {
                    ...data,
                    sourceBranchkey: process.env.MEGA_TECH_SOURCE_BRANCH_KEY,
                }
            }
        )

        return res.data.response.result
    }

    convertToMMDDYYYY = (dateString) => {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return "Invalid Date";
        }
        const mm = String(date.getMonth() + 1).padStart(2, '0');
        const dd = String(date.getDate()).padStart(2, '0');
        const yyyy = date.getFullYear();
        return `${mm}/${dd}/${yyyy}`;
    }

    formatDocumentName = (documentName) => {
        if (documentName === "PASSPORT") {
            return "P"
        } else if (documentName === "DRIVERS_LICENSE") {
            return "D"
        }
        return "P";
    }

    getVeriffDocument = async (userId) => {
        const data = await VeriffDocumentSchema.find({
            userId: new mongoose.Types.ObjectId(userId),
            deletedAt: { $exists: false },
        });
        return data.length > 0 ? data[0] : null;
    };

    generateCashPickupTransaction = async (body, newTransaction, user, recipient, commission, amountToReceive, timezone) => {
        const {
            amount,
            purpose,
            sourceOfIncome,
            serviceId,
            serviceOperatorId,
            payoutMethod,
            destinationCountryId,
            destinationCurrency,
            sourceCountryId,
            sourceCityId
        } = body;

        const senderDetails = {
            senderFName: user.firstName,
            senderLName: user.lastName,
            senderMobileCountryCode: parseInt(user.countryCode ? user.countryCode : 10),
            senderMobile: user.phoneNumber,
            senderDOB: moment(user.birthDate).format("MM/DD/YYYY"),
            sendernationality: user.country,
            sendergender: user.gender.charAt(0).toUpperCase(),
            senderpostalcode: user.zipCode,
            senderemail: user.email,
            senderAddress: user.address,
            sendercity: sourceCityId,
        };

        const recipientDetails = {
            beneficiaryFirstName: recipient.firstName,
            beneficiaryLastName: recipient.lastName,
            beneficiaryMobileCountryCode: parseInt(recipient.countryCode ?? "", 10),
            beneficiaryMobile: recipient.phoneNumber,
            beneficiaryAddress: recipient.address,
            destinationCityId: recipient.cityId,
            Bankname: recipient.bankName,
            beneficiaryBankAccNo: recipient.accountNumber
        };

        const transactionDetails = {
            userCreated: process.env.MEGA_TECH_USERNAME,
            sourceBranchkey: process.env.MEGA_TECH_SOURCE_BRANCH_KEY,
            destinationCountryId: destinationCountryId,
            sendingCurrency: process.env.MEGA_TECH_CURRENCY_TYPE,
            currencyToReceive: destinationCurrency,
            collectionMode: process.env.MEGA_TECH_COLLECTION_MODE,
            serviceId: serviceId,
            serviceOperatorId: serviceOperatorId,
            totalAmount: amount,
            amountToReceive: String(amountToReceive),
            totalCommission: commission,
            purpose: purpose,
            senderRemarks: sourceOfIncome,
            sourceofIncome: sourceOfIncome,
            transactionReferenceNo: newTransaction._id,
            payoutMethod: payoutMethod,
            sourceCountry: sourceCountryId,
        };

        const document = user?.verification?.document;
        let documentDetails = {}

        if (Object.keys(document.toObject()).length === 0) {
            const verrifDocs = await this.getVeriffDocument(user._id);
            if (verrifDocs) {
                documentDetails = {
                    documentName: this.formatDocumentName(verrifDocs.documentType),
                    documentNumber: verrifDocs.documentNumber,
                    Issuer: verrifDocs.documentIssuer,
                    transactionDate: moment.tz(timezone).format('MM/DD/yyyy'),
                    dateofIssue: this.convertToMMDDYYYY(verrifDocs.documentIssueDate),
                    dateofExpire: this.convertToMMDDYYYY(verrifDocs.documentExpireDate),
                };
            }
        } else {
            documentDetails = {
                documentName: this.formatDocumentName(document.type),
                documentNumber: document.number,
                Issuer: document.country,
                transactionDate: moment.tz(timezone).format('MM/DD/yyyy'),
                dateofIssue: this.convertToMMDDYYYY(document.validFrom),
                dateofExpire: this.convertToMMDDYYYY(document.validUntil),
            };
        }

        const requestData = {
            ...senderDetails,
            ...recipientDetails,
            ...transactionDetails,
            ...documentDetails
        };

        const api = {
            "cashpickup": "GenerateCashPickupTransaction",
            "mobilewallet": "GenerateMobileWalletTransaction",
            "banktransfer": "GenerateBankTransaction",
        }

        const res = await this.#callAPI(
            "Services/" + (api[payoutMethod] ?? "GenerateCashPickupTransaction"),
            "post",
            { data: requestData }
        );

        return {
            responseData: res.data.response.result,
            requestData
        };
    };
}

