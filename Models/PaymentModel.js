const PaymentSchema = require("../Database/Schemas/PaymentSchema");

module.exports = class {
	createPayment = (body, userId, totalAmount) => {
		return new PaymentSchema({
			...body,
			type: process.env.NMI_API_PAYMENT_TYPE,
			amount: totalAmount,
			userId,
			createdBy: userId,
			updatedBy: userId
		}).save();
	};

	refundPayment = (body, userId, totalAmount) => {
		return new PaymentSchema({
			...body,
			type: process.env.NMI_API_REFUND_PAYMENT_TYPE,
			amount: totalAmount,
			userId,
			createdBy: userId,
			updatedBy: userId
		}).save();
	};

}