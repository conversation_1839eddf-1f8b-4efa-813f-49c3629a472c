
const ActionLogsSchema = require("../Database/Schemas/ActionLogsSchema");

module.exports = class {
    createActionLog = (body, userId) => {
        return new ActionLogsSchema({
            ...body,
            performedBy: userId,
            createdBy: userId,
            updatedBy: userId
        }).save();
    };

    findActionLogLists = (skip, limit, startDate, endDate, filterValue) => {
        const query = {};

        if (startDate && endDate) {
            query.updatedAt = { $gte: new Date(startDate), $lte: new Date(new Date(endDate).setHours(23, 59, 59, 999)) };
        } else if (startDate) {
            query.updatedAt = { $gte: new Date(startDate) };
        } else if (endDate) {
            query.updatedAt = { $lte: new Date(new Date(endDate).setHours(23, 59, 59, 999)) };
        }

        if (filterValue) {
            query.entity = { $regex: filterValue, $options: "i" };
        }

        return ActionLogsSchema
            .find(query)
            .limit(limit)
            .skip(skip)
            .sort({ updatedAt: "desc" });
    };

    findActionLogEntityLists = () => {
        return ActionLogsSchema.distinct("entity");
    }
}