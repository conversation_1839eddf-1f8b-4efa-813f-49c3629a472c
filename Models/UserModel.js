const UserSchema = require("../Database/Schemas/UserSchema");
const StateSchema = require("../Database/Schemas/StateSchema");

const UserSessionModel = new (require("./UserSessionModel"))();
const RoleModel = new (require("./RoleModel"))();

const {
  PATHS,
  VERIFICATION_STATUS,
  USER_TYPE,
  DURATION_PERIOD_OPTIONS,
} = require("../Configs/constants");
const FileManager = require("../Managers/FileManager");
const mongoose = require("mongoose");
const { changePassword } = require("../Middlewares/Validators/UserValidator");

module.exports = class {
  #defaultOption = {
    lean: true,
    returnConvertedUrl: true,
    populate: [
      {
        path: "roleId",
        select: "_id name canAccessCMS slug",
      },
    ],
  };


  getSingleUser = async (userId) => {
    const aggregationFilter = [
      {
        $match: {
          _id: new mongoose.Types.ObjectId(userId),
          deletedAt: { $exists: false },
        },
      },
      {
        $project: {
          firstName: 1,
          lastName: 1,
          gender: 1,
          birthDate: 1,
          email: 1,
          kycStatus: 1,
          phoneNumber: 1,
          roleId: 1,
          isActive: 1,
          createdAt: 1,
          verification: 1,
          countryCode: 1,
          city: 1,
          cityId: 1,
          state: 1,
          country: 1,
          countryId: 1,
          userSource: 1,
          birthDate: 1,
          profilePicture: 1,
          gender: 1,
          nationality: 1,
          nationalId: 1,
          isCreatedByAdmin: 1,
          isEmailNotificationEnabled: 1,
          nearestRelativeName: 1,
          nearestRelativeEmail: 1,
          nearestRelativePhone: 1,
          nearestRelativeCountryCode: 1,
          zipCode: 1,
          address: 1,
          googleId: 1,
          facebookId: 1,
          appleId: 1,
          password: 1,
          billingAddress: 1,
          billingState: 1,
          billingCity: 1,
          billingZipCode: 1,
        },
      },
      {
        $lookup: {
          from: "veriff_documents",
          localField: "_id",
          foreignField: "userId",
          as: "verifDetails",
        },
      },
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "userId",
          as: "transactionDetails",
        },
      },
      {
        $addFields: {
          totalTransaction: { $size: "$transactionDetails" },
        },
      },
      {
        $unwind: {
          path: "$transactionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$_id",
          firstName: { $first: "$firstName" },
          lastName: { $first: "$lastName" },
          gender: { $first: "$gender" },
          countryId: { $first: "$countryId" },
          birthDate: { $first: "$birthDate" },
          email: { $first: "$email" },
          kycStatus: { $first: "$kycStatus" },
          isActive: { $first: "$isActive" },
          roleId: { $first: "$roleId" },
          totalTransaction: { $first: "$totalTransaction" },
          totalTransferAmount: { $sum: "$transactionDetails.totalAmount" },
          countryCode: { $first: "$countryCode" },
          phoneNumber: { $first: "$phoneNumber" },
          city: { $first: "$city" },
          cityId: { $first: "$cityId" },
          state: { $first: "$state" },
          country: { $first: "$country" },
          userSource: { $first: "$userSource" },
          verifDetails: { $first: "$verifDetails" },
          verification: { $first: "$verification" },
          nearestRelativeName: { $first: "$nearestRelativeName" },
          nearestRelativeEmail: { $first: "$nearestRelativeEmail" },
          nearestRelativePhone: { $first: "$nearestRelativePhone" },
          nearestRelativeCountryCode: { $first: "$nearestRelativeCountryCode" },
          createdAt: { $first: "$createdAt" },
          profilePicture: { $first: "$profilePicture" },
          zipCode: { $first: "$zipCode" },
          address: { $first: "$address" },
          googleId: { $first: "$googleId" },
          facebookId: { $first: "$facebookId" },
          appleId: { $first: "$appleId" },
          password: { $first: "$password" },
          billingAddress: { $first: "$billingAddress" },
          billingState: { $first: "$billingState" },
          billingCity: { $first: "$billingCity" },
          billingZipCode: { $first: "$billingZipCode" },
          isEmailNotificationEnabled: { $first: "$isEmailNotificationEnabled" },
        },
      },
    ];

    let user = await UserSchema.aggregate(aggregationFilter);
    user = user.length > 0 ? user[0] : null;

    // Apply profile picture transformation if user exists
    if (user && user.profilePicture) {
      user.profilePicture = FileManager.getUrl(
        PATHS.USER_PROFILE + PATHS.THUMB,
        user.profilePicture
      );
    }

    return user;
  };


  createUser = async (body, roleSlug) => {
    const role = await RoleModel.findRoleBySlug(roleSlug, "name");

    return (
      await new UserSchema({
        ...body,
        roleId: role._id,
      }).save()
    ).populate({
      path: "roleId",
      select: "-_id name canAccessCMS",
    });
  };

  updateManyUser = async (filter, updateValue) => {
    return UserSchema.updateMany(filter, updateValue);
  };

  deleteUserById = (_id) => {
    return UserSchema.deleteOne({
      _id,
    });
  };

  findUser = (filter, projection, option) => {
    return UserSchema.findOne(filter, projection, option);
  };

  findActiveUser = async (filter, projection, option, isRoleRequired) => {
    const userPromise = isRoleRequired
      ? UserSchema.findOne(
        {
          deletedAt: {
            $exists: false,
          },
          ...filter,
        },
        projection,
        option
      ).populate("roleId")
      : UserSchema.findOne(
        {
          deletedAt: {
            $exists: false,
          },
          ...filter,
        },
        projection,
        option
      );

    const user = await userPromise;

    if (option?.returnConvertedUrl && user?.profilePicture) {
      const profileUrl = FileManager.getUrl(
        PATHS.USER_PROFILE + PATHS.THUMB,
        user.profilePicture
      );
      user.profilePicture = profileUrl;
    }

    return user;
  };

  deleteUserById = (id, update) => {
    return UserSchema.findByIdAndUpdate(id, update);
  };

  getDetailByAuthToken = async (
    authToken,
    projection,
    isRoleRequired = false
  ) => {
    const session = await UserSessionModel.findSessionByAuthToken(authToken);
    if (!session) return {}

    const user = await this.findActiveUser(
      {
        _id: session.userId,
      },
      projection,
      this.#defaultOption,
      isRoleRequired
    );

    return {
      user,
      session
    }
  };

  findUserById = (_id, projection, option) => {
    let opt = this.#defaultOption;

    if (projection) {
      delete opt.populate;
    }

    return this.findActiveUser(
      {
        _id,
      },
      projection,
      option ?? opt
    );
  };

  findUserByEmail = (email, projection, option) => {
    return this.findActiveUser(
      {
        email,
      },
      projection,
      option ?? this.#defaultOption
    );
  };

  findUserBySocialId = (socialId, socialIdKey, projection, option) => {
    return this.findActiveUser(
      {
        [socialIdKey]: socialId,
      },
      projection,
      option ?? this.#defaultOption
    );
  };

  userListAggregation = (
    skip,
    limit,
    startDate,
    endDate,
    filterValue,
    sortKey,
    order,
    state,
    city,
    userSource,
    userType
  ) => {
    const aggregationFilter = [
      {
        $project: {
          firstName: 1,
          lastName: 1,
          email: 1,
          kycStatus: 1,
          phoneNumber: 1,
          roleId: 1,
          isActive: 1,
          createdAt: 1,
          verification: 1,
          countryCode: 1,
          city: 1,
          state: 1,
          country: 1,
          userSource: 1,
          birthDate: 1,
          profilePicture: 1,
          gender: 1,
          state: 1,
          city: 1,
          cityId: 1,
          country: 1,
          countryId: 1,
          nationality: 1,
          nationalId: 1,
          isCreatedByAdmin: 1,
          nearestRelativeName: 1,
          nearestRelativeEmail: 1,
          nearestRelativePhone: 1,
          nearestRelativeCountryCode: 1,
          verification: 1,
          deletedAt: 1,
          googleId: 1,
          facebookId: 1,
          appleId: 1,
          id: "$_id",
        },
      },
      {
        $match: {
          deletedAt: {
            $exists: false,
          },
        },
      },
    ];

    if (userType?.length) {
      aggregationFilter.push({
        $match: {
          roleId: {
            $nin: userType,
          },
        },
      });
    } else {
      aggregationFilter.push({
        $match: {
          roleId: userType,
        },
      });
    }

    aggregationFilter.push({
      $lookup: {
        from: "veriff_documents",
        localField: "_id",
        foreignField: "userId",
        as: "verifDetails",
      },
    });

    if (state) {
      aggregationFilter.push({
        $match: {
          state,
        },
      });
    }

    if (userSource) {
      aggregationFilter.push({
        $match: {
          userSource,
        },
      });
    }

    if (city) {
      aggregationFilter.push({
        $match: {
          city,
        },
      });
    }

    const sortFilter = [];

    if (sortKey) {
      sortFilter.push(
        ...[
          {
            $set: { firstName: { $toLower: "$firstName" } },
          },
          { $sort: { [sortKey]: order } },
        ]
      );
    } else {
      sortFilter.push({ $sort: { createdAt: -1 } });
    }

    if (filterValue) {
      const matchCondition = [];
      filterValue = ((filterValue) =>
        filterValue.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"))(filterValue);
      if (mongoose.isValidObjectId(filterValue)) {
        matchCondition.push({ _id: new mongoose.Types.ObjectId(filterValue) });
      }
      matchCondition.push(
        ...[
          { email: { $regex: filterValue, $options: "i" } },
          { firstName: { $regex: filterValue, $options: "i" } },
          { lastName: { $regex: filterValue, $options: "i" } },
          { phoneNumber: { $regex: filterValue, $options: "i" } },
          { fullName: { $regex: filterValue, $options: "i" } },
        ]
      );

      aggregationFilter.push(
        ...[
          {
            $addFields: {
              fullName: { $concat: ["$firstName", " ", "$lastName"] },
            },
          },
          {
            $match: {
              $or: matchCondition,
            },
          },
        ]
      );
    }

    if (startDate && endDate && endDate.isSameOrAfter(startDate)) {
      aggregationFilter.push({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    const totalCountPipeline = [...aggregationFilter, { $count: "totalUser" }];

    const countTransaction = [
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "userId",
          as: "transactionDetails",
        },
      },
      {
        $addFields: {
          totalTransaction: { $size: "$transactionDetails" },
        },
      },
      {
        $unwind: {
          path: "$transactionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$_id",
          firstName: {
            $first: "$firstName",
          },
          lastName: {
            $first: "$lastName",
          },
          email: {
            $first: "$email",
          },
          kycStatus: {
            $first: "$kycStatus",
          },
          isActive: {
            $first: "$isActive",
          },
          roleId: {
            $first: "$roleId",
          },
          totalTransaction: {
            $first: "$totalTransaction",
          },
          totalTransferAmount: {
            $sum: "$transactionDetails.totalAmount",
          },
          countryCode: {
            $first: "$countryCode",
          },
          phoneNumber: {
            $first: "$phoneNumber",
          },
          city: {
            $first: "$city",
          },
          state: {
            $first: "$state",
          },
          country: {
            $first: "$country",
          },
          userSource: {
            $first: "$userSource",
          },
          verifDetails: {
            $first: "$verifDetails",
          },
          verification: {
            $first: "$verification",
          },
          nearestRelativeName: {
            $first: "$nearestRelativeName",
          },
          nearestRelativeEmail: {
            $first: "$nearestRelativeEmail",
          },
          nearestRelativePhone: {
            $first: "$nearestRelativePhone",
          },
          nearestRelativeCountryCode: {
            $first: "$nearestRelativeCountryCode"
          },
          createdAt: {
            $first: "$createdAt",
          },
          profilePicture: {
            $first: "$profilePicture",
          },
          googleId: { $first: "$googleId" },
          facebookId: { $first: "$facebookId" },
          appleId: { $first: "$appleId" },
        },
      },
    ];

    const pipeline = [...aggregationFilter, ...countTransaction, ...sortFilter];

    if (skip !== undefined && skip !== null && limit) {
      pipeline.push({ $skip: skip }, { $limit: limit });
    }

    return { totalCountPipeline, pipeline };
  };

  subAdminAggregation = (
    skip,
    limit,
    startDate,
    endDate,
    filterValue,
    sortKey,
    order,
    state,
    city,
    userSource,
    userType
  ) => {
    const aggregationFilter = [
      {
        $project: {
          firstName: 1,
          lastName: 1,
          email: 1,
          kycStatus: 1,
          phoneNumber: 1,
          roleId: 1,
          isActive: 1,
          createdAt: 1,
          verification: 1,
          countryCode: 1,
          city: 1,
          state: 1,
          country: 1,
          userSource: 1,
          birthDate: 1,
          profilePicture: 1,
          gender: 1,
          state: 1,
          city: 1,
          cityId: 1,
          country: 1,
          countryId: 1,
          nationality: 1,
          nationalId: 1,
          isCreatedByAdmin: 1,
          nearestRelativeName: 1,
          nearestRelativeEmail: 1,
          nearestRelativePhone: 1,
          nearestRelativeCountryCode: 1,
          verification: 1,
          deletedAt: 1,
          googleId: 1,
          facebookId: 1,
          appleId: 1,
          id: "$_id",
        },
      },
      {
        $match: {
          deletedAt: {
            $exists: false,
          },
        },
      },
    ];

    if (userType?.length) {
      aggregationFilter.push({
        $match: {
          roleId: {
            $nin: userType,
          },
        },
      });
    } else {
      aggregationFilter.push({
        $match: {
          roleId: userType,
        },
      });
    }

    aggregationFilter.push({
      $lookup: {
        from: "veriff_documents",
        localField: "_id",
        foreignField: "userId",
        as: "verifDetails",
      },
    });

    if (state) {
      aggregationFilter.push({
        $match: {
          state,
        },
      });
    }

    if (userSource) {
      aggregationFilter.push({
        $match: {
          userSource,
        },
      });
    }

    if (city) {
      aggregationFilter.push({
        $match: {
          city,
        },
      });
    }

    const sortFilter = [];

    if (sortKey) {
      sortFilter.push(
        ...[
          {
            $set: { firstName: { $toLower: "$firstName" } },
          },
          { $sort: { [sortKey]: order } },
        ]
      );
    } else {
      sortFilter.push({ $sort: { createdAt: -1 } });
    }

    if (filterValue) {
      const matchCondition = [];
      filterValue = ((filterValue) =>
        filterValue.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"))(filterValue);
      if (mongoose.isValidObjectId(filterValue)) {
        matchCondition.push({ _id: new mongoose.Types.ObjectId(filterValue) });
      }
      matchCondition.push(
        ...[
          { email: { $regex: filterValue, $options: "i" } },
          { firstName: { $regex: filterValue, $options: "i" } },
          { lastName: { $regex: filterValue, $options: "i" } },
          { phoneNumber: { $regex: filterValue, $options: "i" } },
          { fullName: { $regex: filterValue, $options: "i" } },
        ]
      );

      aggregationFilter.push(
        ...[
          {
            $addFields: {
              fullName: { $concat: ["$firstName", " ", "$lastName"] },
            },
          },
          {
            $match: {
              $or: matchCondition,
            },
          },
        ]
      );
    }

    if (startDate && endDate && endDate.isSameOrAfter(startDate)) {
      aggregationFilter.push({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    const totalCountPipeline = [...aggregationFilter, { $count: "totalUser" }];

    const countTransaction = [
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "userId",
          as: "transactionDetails",
        },
      },
      {
        $addFields: {
          totalTransaction: { $size: "$transactionDetails" },
        },
      },
      {
        $unwind: {
          path: "$transactionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$_id",
          firstName: {
            $first: "$firstName",
          },
          lastName: {
            $first: "$lastName",
          },
          email: {
            $first: "$email",
          },
          kycStatus: {
            $first: "$kycStatus",
          },
          isActive: {
            $first: "$isActive",
          },
          roleId: {
            $first: "$roleId",
          },
          totalTransaction: {
            $first: "$totalTransaction",
          },
          totalTransferAmount: {
            $sum: "$transactionDetails.totalAmount",
          },
          countryCode: {
            $first: "$countryCode",
          },
          phoneNumber: {
            $first: "$phoneNumber",
          },
          city: {
            $first: "$city",
          },
          state: {
            $first: "$state",
          },
          country: {
            $first: "$country",
          },
          userSource: {
            $first: "$userSource",
          },
          verifDetails: {
            $first: "$verifDetails",
          },
          verification: {
            $first: "$verification",
          },
          nearestRelativeName: {
            $first: "$nearestRelativeName",
          },
          nearestRelativeEmail: {
            $first: "$nearestRelativeEmail",
          },
          nearestRelativePhone: {
            $first: "$nearestRelativePhone",
          },
          nearestRelativeCountryCode: {
            $first: "$nearestRelativeCountryCode"
          },
          createdAt: {
            $first: "$createdAt",
          },
          profilePicture: {
            $first: "$profilePicture",
          },
          googleId: { $first: "$googleId" },
          facebookId: { $first: "$facebookId" },
          appleId: { $first: "$appleId" },
        },
      },
    ];

    const pipeline = [...aggregationFilter, ...countTransaction, ...sortFilter];

    if (skip !== undefined && skip !== null && limit) {
      pipeline.push({ $skip: skip }, { $limit: limit });
    }

    return { totalCountPipeline, pipeline };
  };

  subAdminAggregation = (
    skip,
    limit,
    startDate,
    endDate,
    filterValue,
    sortKey,
    order,
    state,
    city,
    userSource,
    userType
  ) => {
    const aggregationFilter = [
      {
        $project: {
          firstName: 1,
          lastName: 1,
          email: 1,
          kycStatus: 1,
          phoneNumber: 1,
          roleId: 1,
          isActive: 1,
          createdAt: 1,
          updatedAt: 1,
          verification: 1,
          countryCode: 1,
          city: 1,
          state: 1,
          country: 1,
          userSource: 1,
          birthDate: 1,
          profilePicture: 1,
          gender: 1,
          state: 1,
          city: 1,
          cityId: 1,
          country: 1,
          countryId: 1,
          nationality: 1,
          nationalId: 1,
          isCreatedByAdmin: 1,
          nearestRelativeName: 1,
          nearestRelativeEmail: 1,
          nearestRelativePhone: 1,
          nearestRelativeCountryCode: 1,
          verification: 1,
          deletedAt: 1,
          googleId: 1,
          facebookId: 1,
          appleId: 1,
          id: "$_id",
        },
      },
      {
        $match: {
          deletedAt: {
            $exists: false,
          },
        },
      },
    ];

    if (userType?.length) {
      aggregationFilter.push({
        $match: {
          roleId: {
            $nin: userType,
          },
        },
      });
    } else {
      aggregationFilter.push({
        $match: {
          roleId: userType,
        },
      });
    }

    aggregationFilter.push({
      $lookup: {
        from: "veriff_documents",
        localField: "_id",
        foreignField: "userId",
        as: "verifDetails",
      },
    });

    if (state) {
      aggregationFilter.push({
        $match: {
          state,
        },
      });
    }

    if (userSource) {
      aggregationFilter.push({
        $match: {
          userSource,
        },
      });
    }

    if (city) {
      aggregationFilter.push({
        $match: {
          city,
        },
      });
    }

    const sortFilter = [];

    if (sortKey) {
      sortFilter.push(
        ...[
          {
            $set: { firstName: { $toLower: "$firstName" } },
          },
          { $sort: { [sortKey]: order } },
        ]
      );
    } else {
      sortFilter.push({ $sort: { createdAt: -1 } });
    }

    if (filterValue) {
      const matchCondition = [];
      filterValue = ((filterValue) =>
        filterValue.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"))(filterValue);
      if (mongoose.isValidObjectId(filterValue)) {
        matchCondition.push({ _id: new mongoose.Types.ObjectId(filterValue) });
      }
      matchCondition.push(
        ...[
          { email: { $regex: filterValue, $options: "i" } },
          { firstName: { $regex: filterValue, $options: "i" } },
          { lastName: { $regex: filterValue, $options: "i" } },
          { phoneNumber: { $regex: filterValue, $options: "i" } },
          { fullName: { $regex: filterValue, $options: "i" } },
        ]
      );

      aggregationFilter.push(
        ...[
          {
            $addFields: {
              fullName: { $concat: ["$firstName", " ", "$lastName"] },
            },
          },
          {
            $match: {
              $or: matchCondition,
            },
          },
        ]
      );
    }

    if (startDate && endDate && endDate.isSameOrAfter(startDate)) {
      aggregationFilter.push({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    const totalCountPipeline = [...aggregationFilter, { $count: "totalUser" }];

    const countTransaction = [
      {
        $lookup: {
          from: "transactions",
          localField: "_id",
          foreignField: "userId",
          as: "transactionDetails",
        },
      },
      {
        $addFields: {
          totalTransaction: { $size: "$transactionDetails" },
        },
      },
      {
        $unwind: {
          path: "$transactionDetails",
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $group: {
          _id: "$_id",
          firstName: {
            $first: "$firstName",
          },
          lastName: {
            $first: "$lastName",
          },
          email: {
            $first: "$email",
          },
          kycStatus: {
            $first: "$kycStatus",
          },
          isActive: {
            $first: "$isActive",
          },
          roleId: {
            $first: "$roleId",
          },
          totalTransaction: {
            $first: "$totalTransaction",
          },
          totalTransferAmount: {
            $sum: "$transactionDetails.totalAmount",
          },
          countryCode: {
            $first: "$countryCode",
          },
          phoneNumber: {
            $first: "$phoneNumber",
          },
          city: {
            $first: "$city",
          },
          state: {
            $first: "$state",
          },
          country: {
            $first: "$country",
          },
          userSource: {
            $first: "$userSource",
          },
          verifDetails: {
            $first: "$verifDetails",
          },
          verification: {
            $first: "$verification",
          },
          nearestRelativeName: {
            $first: "$nearestRelativeName",
          },
          nearestRelativeEmail: {
            $first: "$nearestRelativeEmail",
          },
          nearestRelativePhone: {
            $first: "$nearestRelativePhone",
          },
          nearestRelativeCountryCode: {
            $first: "$nearestRelativeCountryCode"
          },
          createdAt: {
            $first: "$createdAt",
          },
          updatedAt: {
            $first: "$updatedAt",
          },
          profilePicture: {
            $first: "$profilePicture",
          },
          googleId: { $first: "$googleId" },
          facebookId: { $first: "$facebookId" },
          appleId: { $first: "$appleId" },
        },
      },
    ];

    const pipeline = [...aggregationFilter, ...countTransaction, ...sortFilter];

    if (skip !== undefined && skip !== null && limit) {
      pipeline.push({ $skip: skip }, { $limit: limit });
    }

    return { totalCountPipeline, pipeline };
  };

  getAllUser = async (
    skip,
    limit,
    startDate,
    endDate,
    filterValue,
    sortKey,
    order,
    state,
    city,
    userSource
  ) => {
    const role = await RoleModel.findRoleBySlug(USER_TYPE.USER);

    const { totalCountPipeline, pipeline } = this.userListAggregation(
      skip,
      limit,
      startDate,
      endDate,
      filterValue,
      sortKey,
      order,
      state,
      city,
      userSource,
      role._id
    );

    let total = await UserSchema.aggregate(totalCountPipeline);
    total = total?.[0]?.totalUser ?? 0;
    const users = await UserSchema.aggregate(pipeline);

    const response = { users, total, hasMore: false };

    if (total > skip + limit) response.hasMore = true;

    return response;
  };

  getAllSubAdmin = async (
    skip,
    limit,
    startDate,
    endDate,
    filterValue,
    sortKey,
    order,
    state,
    city,
    userSource,
    adminInfo
  ) => {
    const userRole = await RoleModel.findRoleBySlug(USER_TYPE.USER);

    const { totalCountPipeline, pipeline } = this.subAdminAggregation(
      skip,
      limit,
      startDate,
      endDate,
      filterValue,
      sortKey,
      order,
      state,
      city,
      userSource,
      [userRole._id]
    );

    const adminRoleId = await RoleModel.findRoleBySlug(USER_TYPE.ADMIN);

    const adminRoleFilter = {
      $match: {
        $and: [
          { roleId: { $ne: adminRoleId._id } },
          { _id: { $ne: adminInfo._id } }
        ]
      }
    }

    totalCountPipeline.unshift(adminRoleFilter);
    pipeline.unshift(adminRoleFilter);

    let total = await UserSchema.aggregate(totalCountPipeline);
    total = total?.[0]?.totalUser ?? 0;
    const users = await UserSchema.aggregate(pipeline);

    const response = { users, total, hasMore: false };

    if (total > skip + limit) response.hasMore = true;

    return response;
  };

  addUser = async (userDetails, role) => {
    const createdNewUser = await UserSchema.create({
      firstName: userDetails.firstName,
      lastName: userDetails.lastName,
      email: userDetails.email,
      password: userDetails.password,
      countryCode: userDetails.countryCode,
      address: userDetails.address,
      phoneNumber: userDetails.phoneNumber,
      billingAddress: userDetails.billingAddress,
      billingState: userDetails.billingState,
      billingCity: userDetails.billingCity,
      billingZipCode: userDetails.billingZipCode,
      state: userDetails.state,
      city: userDetails.city,
      cityId: userDetails.cityId,
      birthDate: userDetails.birthDate,
      zipCode: userDetails.zipCode,
      country: userDetails.country,
      countryId: userDetails.countryId,
      nationality: userDetails.nationality,
      nationalId: userDetails.nationalId,
      nearestRelativeName: userDetails.nearestRelativeName,
      nearestRelativeEmail: userDetails.nearestRelativeEmail,
      nearestRelativePhone: userDetails.nearestRelativePhone,
      nearestRelativeCountryCode: userDetails.nearestRelativeCountryCode,
      kycStatus: VERIFICATION_STATUS.APPROVED,
      isActive: true,
      gender: userDetails.gender,
      isCreatedByAdmin: true,
      roleId: role._id,
      createdBy: userDetails.createdBy,
      profilePicture: userDetails?.profilePicture?.[0],
      userSource: "web"
      // TODO: pending with following fields
      /* 
        profilePhoto,
      */
    });

    createdNewUser.password = null;
    return createdNewUser;
  };

  getUserId = async (userId) => {
    const aggregationFilter = [
      {
        $match: {
          _id: new mongoose.Types.ObjectId(userId),
        },
      },
      {
        $lookup: {
          from: "veriff_documents",
          localField: "_id",
          foreignField: "userId",
          as: "verifDetails",
          pipeline: [
            {
              $match: {
                deletedAt: { $exists: false }
              }
            }
          ]
        },
      },
      {
        $project: {
          isUserImported: 0,
          isAppNotificationEnabled: 0,
          isEmailNotificationEnabled: 0,
          createdAt: 0,
          updatedAt: 0,
          isCreatedByAdmin: 0,
          userSource: 0,
          roleId: 0,
          password: 0,
          "verification.sessionToken": 0,
          "verification.url": 0,
          "verification.createdAt": 0,
          "verification.updatedAt": 0,
          "verification.verificationId": 0,
          "verifDetails.userId": 0,
          "verifDetails.createdAt": 0,
          "verifDetails.updatedAt": 0,
        }
      }
    ];

    const userInfo = (await UserSchema.aggregate(aggregationFilter))[0];

    if (userInfo?.verifDetails) {
      userInfo.verifDetails = userInfo.verifDetails.map(doc => ({
        ...doc,
        documentLink: FileManager.getUrl(
          `${PATHS.USER_DOC_VERIFIED_ADMIN}/${userId}`,
          doc.documentName
        )
      }))
    }

    return userInfo;
  };

  importUser = (userData) => {
    return UserSchema.insertMany(userData);
  };

  exportUser = async (
    startDate,
    endDate,
    filterValue,
    sortKey,
    order,
    state,
    city,
    userSource,
    timezone
  ) => {
    const role = await RoleModel.findRoleBySlug(USER_TYPE.USER);
    const { pipeline } = this.userListAggregation(
      null,
      null,
      startDate,
      endDate,
      filterValue,
      sortKey,
      order,
      state,
      city,
      userSource,
      [role._id]
    );

    pipeline.push(
      ...[
        {
          $set: {
            createdAt: {
              $dateToString: {
                format: "%Y-%m-%dT%H:%M:%S",
                date: "$createdAt",
                timezone: timezone ?? "UTC",
              },
            },
          },
        },
        {
          $project: {
            _id: 0,
            roleId: 0,
            verification: 0,
            verifDetails: 0,
            profilePicture: 0
          },
        },
      ]
    );

    const users = UserSchema.aggregate(pipeline).cursor();

    return users;
  };

  updateUser = (userId, userData) => {
    return UserSchema.findOneAndUpdate(
      {
        _id: new mongoose.Types.ObjectId(userId),
      },
      userData,
      { new: true }
    );
  };

  bulkUpdate = (filter, userData) => {
    return UserSchema.updateMany(filter, userData);
  };

  getStates = async (skip, limit, searchValue) => {
    const filter = searchValue
      ? {
        $or: [
          { name: { $regex: searchValue, $options: "i" } },
          { code: { $regex: searchValue, $options: "i" } },
        ],
      }
      : {};

    const total = await StateSchema.countDocuments(filter);

    const data = await StateSchema.find(filter)
      .select("+isActive")
      .sort({ isActive: -1, name: 1 })
      .limit(limit)
      .skip(skip);

    return { total, data };
  };

  getActiveState = async (stateName) => {
    return StateSchema.exists({
      isActive: true,
      name: { $regex: new RegExp(`^${stateName}$`, "i") },
    });
  };

  toggleStateStatus = (filter, updatedData) => {
    return StateSchema.updateOne(filter, updatedData);
  };

  getUniqueSources = () => {
    return UserSchema.distinct("userSource", { deletedAt: { $exists: false } });
  };

  getUniqueStates = () => {
    return UserSchema.distinct("state", { deletedAt: { $exists: false } });
  };

  getUniqueSources = () => {
    return UserSchema.distinct("userSource", { deletedAt: { $exists: false } });
  };

  getUserEmailList = (emailList) => {
    return UserSchema.find(
      {
        email: {
          $in: emailList,
        },
        deletedAt: {
          $exists: false,
        },
      },
      {
        email: 1,
      }
    );
  };

  getUserCount = (startDate, endDate, source, durationPeriod) => {
    let groupId = {
      createdYear: "$createdYear",
      createdMonth: "$createdMonth",
    }

    if (DURATION_PERIOD_OPTIONS.DAY === durationPeriod) {
      groupId.createdDay = "$createdDay"
    }

    const aggregationFilter = [
      {
        $project: {
          createdMonth: {
            $month: "$createdAt",
          },
          createdYear: {
            $year: "$createdAt",
          },
          createdDay: {
            $dayOfMonth: "$createdAt",
          },
        },
      },
      {
        $group: {
          _id: groupId,
          userCount: {
            $sum: 1,
          },
        },
      },
      {
        $project: {
          _id: 0,
          userCount: 1,
          createdMonth: "$_id.createdMonth",
          createdYear: "$_id.createdYear",
          createdDay: "$_id.createdDay",
        },
      },
    ];

    if (startDate && endDate) {
      aggregationFilter.unshift({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    if (source) {
      aggregationFilter.unshift({
        $match: {
          userSource: source
        }
      })
    }

    return UserSchema.aggregate(aggregationFilter);
  };
};
