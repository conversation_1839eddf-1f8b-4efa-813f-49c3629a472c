const VeriffDocumentSchema = require("../Database/Schemas/VeriffDocumentSchema");

module.exports = class {
  addDocument = (body, userId, verificationId) => {
    return new VeriffDocumentSchema({
      ...body,
      userId,
      verificationId,
      createdBy: userId,
      updatedBy: userId,
    }).save();
  };

  addAdminCreatedUserDocument = (userId, docData) => {
    const docsDetails = {
      ...docData,
    };

    return Promise.all(docData.files?.map((doc) => {
      return VeriffDocumentSchema.create(
        {
          ...docsDetails,
          documentName: doc,
          userId,
        },
      );
    }));
  };

  updateAdminCreatedUserDocument = (userId, docData) =>
    VeriffDocumentSchema.updateMany({ userId }, { $set: docData });

  createDocumentDownloadList = (userId) => {
    return VeriffDocumentSchema.find({
      userId: new mongoose.Types.ObjectId(userId),
      deletedAt: {
        $exists: false,
      },
    });
  };

  editDocument = (userId, updateData) => {
    return VeriffDocumentSchema.findOneAndUpdate(
      {
        userId: new mongoose.Types.ObjectId(userId),
      },
      updateData,
      {
        new: true,
      }
    );
  };

  deletedDocument = async (documentIds, userId) => {

    documentIds = Array.isArray(documentIds) ? documentIds : [documentIds];
    const documentLists = await VeriffDocumentSchema.find(
      {
        _id: {
          $in: documentIds.map((id) => new mongoose.Types.ObjectId(id)),
        },
        userId,
        deletedAt: {
          $exists: false,
        },
      },
      {
        documentName: 1
      }
    );


    await VeriffDocumentSchema.updateMany(
      {
        _id: {
          $in: documentIds.map((id) => new mongoose.Types.ObjectId(id)),
        },
        userId,
        deletedAt: {
          $exists: false,
        },
      },
      {
        deletedAt: new Date(),
      }
    );

    return documentLists;
  };
};
