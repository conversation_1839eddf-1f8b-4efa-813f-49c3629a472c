const TransactionSchema = require("../Database/Schemas/TransactionSchema");
const TransactionLimitSchema = require("../Database/Schemas/TransactionLimitSchema");
const mongoose = require("mongoose");
const { PAYMENT_STATUS_TYPE, DURATION_PERIOD_OPTIONS } = require("../Configs/constants");
module.exports = class {
  findTransactionById = async (_id, projection) => {
    return TransactionSchema.findById(_id, projection);
  };

  findTransactionByTransactionId = async (id) => {
    return TransactionSchema.findOne({ transactionId: id });
  };

  findTransactionByFilter = async (filter, projection, options) => {
    return TransactionSchema.findOne(
      filter,
      projection,
      options ?? { lean: true }
    ).populate({
      path: "recipientId",
    });
  };

  createTransaction = (
    recipientId,
    cardId,
    body,
    transactioData,
    userId,
    paymentId,
    finalAmount,
    exchangeRate,
    stateName,
    sourceCountryName,
    sourceCountryId,
    sourceCityName,
    sourceCityId,
    paymentCityName,
  ) => {
    return new TransactionSchema({
      transactionRefNo: transactioData.TransactionRefNo ? transactioData.TransactionRefNo : transactioData.TransactionNo,
      transactionId: transactioData.TransactionId ? transactioData.TransactionId : transactioData.TransactionNo,
      bankReferenceNumber: transactioData.BankReferenceNumber,
      myCashReferenceNumber: transactioData.MyCashReferenceNumber,
      senderKey: transactioData.SenderKey,
      receiverKey: transactioData.ReceiverKey,
      senderNationality: body.sendernationality,
      senderCity: body.sendercity,
      senderGender: body.sendergender,
      senderPostalCode: body.senderpostalcode,
      senderEmail: body.senderemail,
      senderOccupation: body.senderoccupation,
      sourceOfIncome: body.sourceofIncome,
      dateOfIssue: body.dateofIssue,
      dateOfExpire: body.dateofExpire,
      ...body,
      ...transactioData,
      exchangeRate,
      stateName,
      sourceCountryName,
      sourceCountryId,
      sourceCityName,
      sourceCityId,
      paymentCityName,
      userId,
      paymentId,
      recipientId,
      cardId,
      createdBy: userId,
      updatedBy: userId,
    }).save();
  };

  createListAggregation = (
    userId,
    startDate,
    endDate,
    collectionMode,
    payoutMethod,
    paymentStatus,
    country,
    searchValue,
    recipientId,
    state
  ) => {
    const transactionAggregation = [
      {
        $addFields: {
          sentAmount: {
            $toString: "$totalAmount",
          },
          receivedAmount: {
            $toString: "$amountToReceive",
          },
        },
      },
      {
        $lookup: {
          from: "recipients",
          localField: "recipientId",
          foreignField: "_id",
          as: "recipient",
        },
      },
      {
        $lookup: {
          from: "user_cards",
          localField: "cardId",
          foreignField: "_id",
          as: "cards",
        },
      },
    ];

    if (userId) {
      transactionAggregation.push({
        $match: {
          userId,
        },
      });
    }

    if (
      startDate &&
      endDate &&
      moment(endDate, "YYYY-MM-DD").isSameOrAfter(startDate)
    ) {
      transactionAggregation.push({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    if (collectionMode) {
      transactionAggregation.push({
        $match: {
          collectionMode,
        },
      });
    }
    if (payoutMethod) {
      transactionAggregation.push({
        $match: {
          payoutMethod,
        },
      });
    }
    if (country) {
      transactionAggregation.push({
        $match: {
          destinationCountryId: parseInt(country),
        },
      });
    }

    if (paymentStatus) {
      transactionAggregation.push({
        $match: {
          paymentStatus,
        },
      });
    }

    if (searchValue) {
      transactionAggregation.unshift(...[
        {
          $addFields: {
            fullSenderName: { $concat: ["$senderFName", " ", "$senderLName"] },
            fullBeneficiaryName: { $concat: ["$beneficiaryFirstName", " ", "$beneficiaryLastName"] }
          },
        },
        {
          $match: {
            $or: [
              { email: { $regex: searchValue, $options: "i" } },
              { senderFName: { $regex: searchValue, $options: "i" } },
              { senderLName: { $regex: searchValue, $options: "i" } },
              { beneficiaryFirstName: { $regex: searchValue, $options: "i" } },
              { beneficiaryLastName: { $regex: searchValue, $options: "i" } },
              { fullSenderName: { $regex: searchValue, $options: "i" } },
              { fullBeneficiaryName: { $regex: searchValue, $options: "i" } },
              { transactionId: { $regex: searchValue, $options: "i" } },
            ],
          },
        },
        {
          $project: {
            fullSenderName: 0,
            fullBeneficiaryName: 0,
          }
        }
      ]);
    }

    if (state) {
      transactionAggregation.push({
        $match: {
          stateName: state,
        },
      });
    }

    if (recipientId && mongoose.isValidObjectId(recipientId)) {
      transactionAggregation.push({
        $match: {
          recipientId: new mongoose.Types.ObjectId(recipientId),
        },
      });
    }

    const totalCountPipeline = [
      ...transactionAggregation,
      {
        $group: {
          _id: null,
          totalAmountSendByPlatform: { $sum: { $toDouble: "$totalAmount" } },
          numberOfTransaction: { $sum: 1 },
        },
      },
    ];

    return { totalCountPipeline, transactionAggregation };
  };

  transactionMatrix = async (
    transactionType,
    userId,
    startDate,
    endDate,
    collectionMode,
    payoutMethod,
    country,
    state,
    searchValue,
    recipientId) => {
    const transactionAggregation = [
      {
        $match: {
          paymentStatus: transactionType,
        },
      },
    ];

    if (userId) {
      transactionAggregation.push({
        $match: {
          userId,
        },
      });
    }

    if (
      startDate &&
      endDate &&
      moment(endDate, "YYYY-MM-DD").isSameOrAfter(startDate)
    ) {
      transactionAggregation.push({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    if (collectionMode) {
      transactionAggregation.push({
        $match: {
          collectionMode,
        },
      });
    }
    if (payoutMethod) {
      transactionAggregation.push({
        $match: {
          payoutMethod,
        },
      });
    }
    if (country) {
      transactionAggregation.push({
        $match: {
          destinationCountryId: parseInt(country),
        },
      });
    }

    if (state) {
      transactionAggregation.push({
        $match: {
          stateName: state,
        },
      });
    }

    if (recipientId && mongoose.isValidObjectId(recipientId)) {
      transactionAggregation.push({
        $match: {
          recipientId: new mongoose.Types.ObjectId(recipientId),
        },
      });
    }

    if (searchValue) {
      transactionAggregation.push(...[
        {
          $addFields: {
            fullSenderName: { $concat: ["$senderFName", " ", "$senderLName"] },
            fullBeneficiaryName: { $concat: ["$beneficiaryFirstName", " ", "$beneficiaryLastName"] }
          },
        },
        {
          $match: {
            $or: [
              { email: { $regex: searchValue, $options: "i" } },
              { senderFName: { $regex: searchValue, $options: "i" } },
              { senderLName: { $regex: searchValue, $options: "i" } },
              { beneficiaryFirstName: { $regex: searchValue, $options: "i" } },
              { beneficiaryLastName: { $regex: searchValue, $options: "i" } },
              { fullSenderName: { $regex: searchValue, $options: "i" } },
              { fullBeneficiaryName: { $regex: searchValue, $options: "i" } },
              { transactionId: { $regex: searchValue, $options: "i" } },
            ],
          },
        },
        {
          $project: {
            fullSenderName: 0,
            fullBeneficiaryName: 0,
          }
        }
      ]);
    }

    if (recipientId && mongoose.isValidObjectId(recipientId)) {
      transactionAggregation.push({
        $match: {
          recipientId: new mongoose.Types.ObjectId(recipientId),
        },
      });
    }

    transactionAggregation.push({
      $group: {
        _id: null,
        totalAmountSendByPlatform: { $sum: { $toDouble: "$totalAmount" } },
        numberOfTransaction: { $sum: 1 },
      },
    });

    return TransactionSchema.aggregate(transactionAggregation);
  };

  listTransaction = async function (
    userId,
    limit,
    skip,
    startDate,
    endDate,
    collectionMode,
    payoutMethod,
    paymentStatus,
    country,
    searchValue,
    recipientId,
    state,
    forCms = false
  ) {
    const { totalCountPipeline, transactionAggregation } =
      this.createListAggregation(
        userId,
        startDate,
        endDate,
        collectionMode,
        payoutMethod,
        paymentStatus,
        country,
        searchValue,
        recipientId,
        state
      );

    const total = await TransactionSchema.aggregate(totalCountPipeline);

    const totalPlatFormVolume = total?.[0]?.totalAmountSendByPlatform ?? 0;

    let totalCompleteTransaction, totalOutStandingTransaction;
    if (forCms) {
      totalCompleteTransaction = await this.transactionMatrix(
        PAYMENT_STATUS_TYPE.COMPLETED,
        userId,
        startDate,
        endDate,
        collectionMode,
        payoutMethod,
        country,
        state,
        searchValue,
        recipientId,
      );
      totalOutStandingTransaction = await this.transactionMatrix(
        PAYMENT_STATUS_TYPE.PROCESSING,
        userId,
        startDate,
        endDate,
        collectionMode,
        payoutMethod,
        country,
        state,
        searchValue,
        recipientId,
      );
    }

    transactionAggregation.push({
      $project: {
        totalAmount: 0,
        amountToReceive: 0,
      },
    });

    const transactionCount = total?.[0]?.numberOfTransaction ?? 0;

    const transactions = await TransactionSchema.aggregate([
      ...transactionAggregation,
      { $sort: { createdAt: -1 } },
      { $skip: skip },
      { $limit: limit },
    ]);

    const data = {
      transactionCount,
      transactions,
      hasMore: false,
    };

    if (forCms) {
      data.totalCompletedTransaction =
        totalCompleteTransaction?.[0]?.numberOfTransaction ?? 0;
      data.totalCompletedVolume =
        totalCompleteTransaction?.[0]?.totalAmountSendByPlatform ?? 0;
      data.totalOutStandingTransaction =
        totalOutStandingTransaction?.[0]?.numberOfTransaction ?? 0;
      data.totalOutStandingVolume =
        totalOutStandingTransaction?.[0]?.totalAmountSendByPlatform ?? 0;
      data.totalPlatFormVolume = forCms ? totalPlatFormVolume : 0;
    }

    if (transactionCount >= skip + limit) data.hasMore = true;

    return data;
  };

  exportTransactionList = (
    userId,
    startDate,
    endDate,
    collectionMode,
    payoutMethod,
    paymentStatus,
    country,
    searchValue,
    recipientId,
    state,
    timezone
  ) => {
    const { transactionAggregation } = this.createListAggregation(
      userId,
      startDate,
      endDate,
      collectionMode,
      payoutMethod,
      paymentStatus,
      country,
      searchValue,
      recipientId,
      state
    );

    const transactionCursor = TransactionSchema.aggregate([
      ...transactionAggregation,
      ...[
        {
          $set: {
            transactionTime: {
              $dateToString: {
                format: "%Y-%m-%dT%H:%M:%S",
                date: "$createdAt",
                timezone: timezone ?? "UTC",
              },
            },
          },
        },

        {
          $project: {
            "Transaction Id": "$transactionId",
            "Amount": "$totalAmount",
            "Transaction Type": "$payoutMethod",
            "Sender First Name": "$senderFName",
            "Sender Last Name": "$senderLName",
            "Source City": "$paymentCityName",
            "State Name": "$stateName",
            "Source Country": "$sourceCountryName",
            "Recipient First Name" : "$beneficiaryFirstName",
            "Recipient Last Name" : "$beneficiaryLastName",
            "Recipient State": "$recipient.state",
            "Recipient Country": "$recipient.country",
            "Transaction Time": "$transactionTime",
            "Transaction Status": "$paymentStatus",
            "Reason" :  "$purpose",
            _id: 0,
          },
        },
      ],
    ]).cursor();

    return transactionCursor;
  };

  getUniqueValueByField = async (field) => {
    return TransactionSchema.distinct(field);
  };

  getCountTotalNumberOfTransaction = async () => {
    return TransactionSchema.countDocuments({
      deletedAt: {
        $exists: false,
      },
    });
  };

  getTransactionLimit = async () => {
    return TransactionLimitSchema.find();
  };

  updateTransactionLimit = async (filter, updatedData) => {
    return TransactionLimitSchema.updateOne(filter, updatedData);
  };

  getTransactionInsight = async (startDate, endDate, filter, durationPeriod) => {

    let groupId = {
      transactionYear: "$transactionYear",
      transactionMonth: "$transactionMonth",
    }

    if (DURATION_PERIOD_OPTIONS.DAY === durationPeriod) {
      groupId.transactionDay = "$transactionDay"
    }

    const aggregationFilter = [
      {
        $project: {
          transactionYear: {
            $year: "$createdAt",
          },
          transactionMonth: {
            $month: "$createdAt",
          },
          transactionDay: {
            $dayOfMonth: "$createdAt",
          },
        },
      },
      {
        $group: {
          _id: groupId,
          transactionCount: {
            $sum: 1,
          },
        },
      },
      {
        $project: {
          _id: 0,
          transactionCount: 1,
          transactionYear: "$_id.transactionYear",
          transactionMonth: "$_id.transactionMonth",
          transactionDay: "$_id.transactionDay"
        },
      },
    ];

    if (startDate && endDate) {
      aggregationFilter.unshift({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    if (filter) aggregationFilter.unshift(filter);

    return TransactionSchema.aggregate(aggregationFilter);
  };

  getTransactionInsightForPDF = async (startDate, endDate, filter) => {
    const aggregationFilter = [];

    if (startDate && endDate) {
      aggregationFilter.push({
        $match: {
          createdAt: {
            $gte: new Date(startDate.toDate().setHours(0, 0, 0, 0)),
            $lte: new Date(endDate.toDate().setHours(23, 59, 59, 999)),
          },
        },
      });
    }

    if (filter?.$match) {
      aggregationFilter.push({ $match: filter.$match });
    }

    aggregationFilter.push(
      {
        $group: {
          _id: null,
          totalTransactionCount: { $sum: 1 },
          totalAmountSum: { $sum: "$totalAmount" },
        },
      },
      {
        $project: {
          _id: 0,
          totalTransactionCount: 1,
          totalAmountSum: 1,
        },
      }
    );

    return TransactionSchema.aggregate(aggregationFilter);
  };

};
