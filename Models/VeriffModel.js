const axios = require("axios");

module.exports = class {

    #headers = (extraHeaders = {}) => ({
        "Content-Type": "application/json",
        "x-auth-client": process.env.VERIFF_API_KEY,
        ...extraHeaders
    });

    #callAPI = async (path, method, options = {}) => {
        const { data = {}, headers = {} } = options;

        const config = {
            url: `${process.env.VERIFF_BASE_URL}/${path}`,
            method,
            data,
            headers: this.#headers(headers)
        };

        try {
            return await axios.request(config);
        }
        catch (error) {
            console.log(error.response.data);
            throw new Error(error.response.data)
        }
    };

    createVeriffSessions = async (user) => {
        const response = await this.#callAPI("v1/sessions", "post", {
            data: {
                verification: {
                    callback: process.env.WEB_URL,
                    person: {
                        firstName: user.firstName,
                        lastName: user.lastName,
                    },
                    vendorData: user.email
                }
            }
        });
        return response.data;
    };

    getMediaSession = async (verificationId, signature) => {
        const response = await axios.get(
            `${process.env.VERIFF_BASE_URL}/v1/sessions/${verificationId}/media`,
            {
                headers: {
                    "x-auth-client": process.env.VERIFF_API_KEY,
                    "x-hmac-signature": signature
                }
            }
        );
        return response.data;
    };

    fetchMediaDetails = async (mediaId, signature) => {
        try {
            const response = await axios.get(
                `${process.env.VERIFF_BASE_URL}/v1/media/${mediaId}`,
                {
                    headers: {
                        "x-auth-client": process.env.VERIFF_API_KEY,
                        "x-hmac-signature": signature,
                    },
                    responseType: 'arraybuffer',
                }
            );
            const base64 = Buffer.from(response.data, 'binary').toString('base64');
            return base64;
        } catch (error) {
            console.error("Error fetching media details:", error);
            throw error;
        }
    };

};
