const StaticPageSchema = require("../Database/Schemas/StaticPageSchema")

module.exports = class {

    listPages = (filter, projection, option) => {
        return StaticPageSchema.find(filter, projection, option)
    }

    findPage = (filter, projection, option) => {
        return StaticPageSchema.findOne(filter, projection, option)
    }

    updatePageBySlug = (slug, body) => {
        return StaticPageSchema.updateOne(
            {
                slug
            },
            body
        )
    }

    findPageBySlug = (slug, projection, option) => {
        return StaticPageSchema.findOne({
            slug
        }, projection, option)
    }

}
