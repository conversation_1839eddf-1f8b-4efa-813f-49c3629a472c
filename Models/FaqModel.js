const FaqSchema = require("../Database/Schemas/FaqSchema")

module.exports = class {

    findFaqs = (filter, projection, options) => {
        return FaqSchema.find(
            this.#getMatchCondition(filter),
            projection,
            options ?? {
                lean: true
            }
        )
    }

    #getMatchCondition = (filter) => {
        const {
            searchKey,
        } = filter

        const match = {            
            deletedAt:{
                $exists: false
            }
        }

        if (searchKey) {
            const searchRegExp = new RegExp(searchKey, "i")
            
            match.$or = [
                { question: searchRegExp },
                { answer: searchRegExp },
                {
                    topics: {
                        $elemMatch: { $regex: searchKey, $options: 'i' }
                    }
                }
            ]
        }

        return match
    }

    createFaq = (faqDetails) => {
        return FaqSchema.create(faqDetails);
    }

    updateFaq = (updatedFaq, faqId) => {
        return FaqSchema.findOneAndUpdate(
          {
            _id: new mongoose.Types.ObjectId(faqId),
          },
          updatedFaq,
          { new: true }
        );
    };

}
