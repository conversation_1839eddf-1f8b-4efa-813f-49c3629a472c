const axios = require("axios");
const querystring = require('querystring');

module.exports = class {

    #headers = (extraHeaders = {}) => ({
        "Content-Type": "application/x-www-form-urlencoded",
        ...extraHeaders
    });

    #callAPI = async (path, method, options = {}) => {
        const { data = {}, headers = {} } = options;
        data.security_key = process.env.NMI_API_SECRET

        const config = {
            url: `${process.env.NMI_BASE_URL}/${path}`,
            method,
            data,
            headers: this.#headers(headers)
        };

        try {
            return await axios.request(config);
        }
        catch (error) {
            console.log(error.response.data);
            throw new Error(error.response.data)
        }
    };

    createPayment = async (tokenData, user) => {
        const response = await this.#callAPI("", "post", {
            data:
            {
                ...tokenData,
                phone: user.phoneNumber,
                city: user.city,
                state: user.state,
                country: user.country,
                zipcode: user.zipCode,
                address1: user.billingAddress || user.address,
                type: process.env.NMI_API_PAYMENT_TYPE
            }
        });
        return querystring.parse(response.data)
    };

    refundPayment = async (tokenData, user) => {
        const response = await this.#callAPI("", "post", {
            data:
            {
                ...tokenData,
                phone: user.phoneNumber,
                city: user.city,
                state: user.state,
                country: user.country,
                zipcode: user.zipCode,
                type: process.env.NMI_API_REFUND_PAYMENT_TYPE
            }
        });
        return querystring.parse(response.data)
    }

    createCustomerToken = async (tokenData) => {
        const response = await this.#callAPI("", "post", {
            data: tokenData
        });

        return querystring.parse(response.data)
    };

};
