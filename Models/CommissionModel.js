const { MULTIPLIER_TYPE } = require("../Configs/constants");
const CommissionSchema = require("../Database/Schemas/CommissionSchema");

module.exports = class {
    updateCommissionValue = (id, feeRate) => {
        return CommissionSchema.updateOne(
            {
                _id: id,
            },
            {
                feeRate
            }
        );
    }

    listCommissions = () => {
        return CommissionSchema.find({});
    }

    findCommissionByServiceId = (serviceId, projection, option) => {
        return CommissionSchema.findOne({ serviceId }, projection, option);
    }

    getChargesInObject = async () => {
        const charges = await this.listCommissions()

        return charges.reduce((data, charge) => {
            data[charge.slug] = {
                feeRate: charge.feeRate,
                multiplier: charge.multiplier,
            }
            return data
        }, {})
    }

    calculateFinalCharge = async (amount, chargeObj) => {
        let finalCharge = 0
        let charges = chargeObj

        if (!charges) {
            charges = await this.getChargesInObject()
        }
        
        Object.values(charges).forEach(charge => {
            switch (charge.multiplier) {
                case MULTIPLIER_TYPE.PERCENTAGE: {
                    finalCharge += (amount * charge.feeRate * 0.01)
                    break
                }
                case MULTIPLIER_TYPE.FLAT: {
                    finalCharge += charge.feeRate
                    break
                }
            }
        })

        finalCharge = Math.round(finalCharge * 100) / 100
        const finalAmount = Math.round((Number(amount) + finalCharge) * 100) / 100
        
        return {
            finalAmount,
            finalCharge
        }
    }

}
