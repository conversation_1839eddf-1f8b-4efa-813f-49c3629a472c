const  mongoose = require("mongoose");
const ModuleSchema = require("../Database/Schemas/ModuleSchema");

module.exports = class{
    async verifyPermission(moduleName, roleId){
        return ModuleSchema.findOne({
            slug: moduleName,
            role: roleId,
            deletedAt: {
                $exists: false
            },
        })
    }

    async updateRolePermission(roleId, updateValue){
        
        const updates = updateValue.map(moduleInfo => ({
            updateOne: {
                filter: {
                    name: moduleInfo.name,
                    role: new mongoose.Types.ObjectId(roleId),
                },
                update:{
                    $set: {
                        view: moduleInfo.view,
                        add: moduleInfo.add,
                        delete: moduleInfo.delete,
                        edit: moduleInfo.edit
                    }
                }
            }
        }));
        return ModuleSchema.bulkWrite(updates);
    }

    async getRolesByPermission(roleId){
        return ModuleSchema.find({
            role: new mongoose.Types.ObjectId(roleId)
        });
    }

    async insertMany(permissions){
        return ModuleSchema.insertMany(permissions);
    }

    async updateModules(filter, updateValue){
        return ModuleSchema.updateMany(filter, updateValue);
    }

}