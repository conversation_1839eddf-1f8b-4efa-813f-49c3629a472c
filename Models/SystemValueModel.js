const SystemValueSchema = require("../Database/Schemas/SystemValueSchema")

module.exports = class {

    findValue = (filter, projection, option) => {
        return SystemValueSchema.findOne(filter, projection, option)
    }

    findValueBySlug = (slug, projection, option) => {
        return this.findValue(
            {
                slug
            },
            projection,
            option ?? {
                lean: true
            }
        )
    }

}
