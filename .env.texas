PROJECT_NAME="AmalPay Texas"
PORT=8400
REGION="texas"

WEB_URL = "https://texas.amalpay.com"
JWT_SECRET = 313d8b0eaab1e837d2829109aec83b506968cd04b9be41da9396e0e2f373e3b3

ENCRYPTION_ALGORITHM="aes192"
ENCRYPTION_KEY="qsd@5c$fg9_yjv-3m15e7"
ENCRYPTION_SALT_ROUNDS = 10

SAVE_LOG = true

MONGODB_URL = mongodb+srv://AmalBackend:<EMAIL>/
DB_NAME=amal_pay_texas

ENVIRONMENT=production

AWS_SMTP_HOST = "email-smtp.us-east-1.amazonaws.com"
AWS_SMTP_USERNAME = "AKIAZ3MGM4J4QS33LP3P"
AWS_SMTP_PASSWORD = "BF4706QPsQ8P4HoQ/3ZPQXkbs0KYvinXYaUeltnI0VTa"
EMAIL_ADDRESS="<EMAIL>"
PDF_RECEIVER_EMAIL="<EMAIL>"

# Regional specific configurations
REGION_CODE="TX"
REGION_NAME="Texas"
ALLOWED_STATES=["Texas"]
ALLOWED_COUNTRIES=["United States"]
