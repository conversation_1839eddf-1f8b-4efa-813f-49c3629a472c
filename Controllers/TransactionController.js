const TransactionSchema = new (require("../Models/TransactionsModel"))();
const { DEFAULT_LIMIT, USER_TYPE } = require("../Configs/constants");
const fastCsv = require("fast-csv");
const moment = require("moment");

module.exports = class {
  listTransaction = async (req, res) => {
    try {
      const limit = parseInt(req.query.limit ?? DEFAULT_LIMIT);
      const skip = ((req.query.page ?? 1) - 1) * limit;
      let userId;

      if (
        (req?.admin?.roleId?.slug == USER_TYPE.ADMIN ||
          req?.admin?.roleId?.slug == USER_TYPE.SUB_ADMIN) &&
        req.query.userId
      ) {
        userId = new mongoose.Types.ObjectId(req.query.userId);
      } else {
        userId = req?.user?._id;
      }

      const forCms = !req.user;
      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;

      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const response = await TransactionSchema.listTransaction(
        userId,
        limit,
        skip,
        startDate,
        endDate,
        req.query.collectionMode,
        req.query.payoutMethod,
        req.query.paymentStatus,
        req.query.country,
        req.query.searchValue,
        req.query.recipientId,
        req.query.state,
        forCms
      );

      return res.handler.success(response);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  exportTransaction = async (req, res) => {
    try {
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="transaction_export.csv"`
      );

      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;

      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const cursor = TransactionSchema.exportTransactionList(
        req.query.userId,
        startDate,
        endDate,
        req.query.collectionMode,
        req.query.payoutMethod,
        req.query.paymentStatus,
        req.query.country,
        req.query.searchValue,
        req.query.recipientId,
        req.query.state,
        req.query.timezone
      );

      const csvStream = fastCsv.format({ headers: true });

      csvStream.pipe(res);
      cursor.on("data", (user) => {
        csvStream.write(user);
      });

      cursor.on("end", () => {
        csvStream.end();
        console.log("CSV export completed.");
      });

      cursor.on("error", (err) => {
        console.error("Error exporting CSV:", err.message);
        res.handler.serverError();
      });
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  distinctPayoutMethod = async (req, res) => {
    try {
      const payoutMethod = await TransactionSchema.getUniqueValueByField(
        "payoutMethod"
      );
      return res.handler.success(payoutMethod);
    } catch (err) {
      return res.handler.serverError();
    }
  };

  async getTransactionNumbers(req, res, next) {
    try {
      const numberOfTransaction =
        await TransactionSchema.getCountTotalNumberOfTransaction();
      return res.handler.success(numberOfTransaction);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async getTransactionLimit(req, res, next) {
    try {
      const transactionLimit = await TransactionSchema.getTransactionLimit();
      return res.handler.success(transactionLimit);
    } catch (error) {
      return res.handler.serverError(error);
    }
  }

  async updateTransactionLimit(req, res, next) {
    try {
      await TransactionSchema.updateTransactionLimit(
        {
          _id: req.query.transactionLimitId,
        },
        req.body
      );
      return res.handler.success();
    } catch (error) {
      return res.handler.serverError(error);
    }
  }
};
