const RecipientModel = new (require("../Models/RecipientModel"))()
const ActionLogModel = new (require("../Models/ActionLogModel"))();

const { LOG_ACTION_TYPE } = require("../Configs/constants");
module.exports = class {

    listRecipient = async (req, res) => {
        try {
            const recipients = await RecipientModel.listRecipientByUserId(req.user._id, req.query)
            return res.handler.success(recipients)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    createRecipient = async (req, res) => {
        try {
            const newRecipient = await RecipientModel.createRecipient(req.body, req.user._id)
            await ActionLogModel.createActionLog(
                {
                    entity: 'recipient',
                    description: `${newRecipient.firstName} ${newRecipient.lastName} recipient is created`,
                    entityId: newRecipient._id,
                    firstName: newRecipient.firstName,
                    lastName: newRecipient.lastName,
                    email: newRecipient.email,
                    action: LOG_ACTION_TYPE.CREATE,
                },
                req.user._id,
            );
            return res.handler.success(newRecipient)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    updateRecipient = async (req, res) => {
        try {
            const updateRecipient = await RecipientModel.updateRecipient(
                {
                    _id: req.query.recipientId
                },
                req.body
            )

            if (updateRecipient) {
                const recipientDetails = await RecipientModel.findRecipientById(req.query.recipientId);
                await ActionLogModel.createActionLog(
                    {
                        entity: 'recipient',
                        description: `Recipient ${recipientDetails.firstName} ${recipientDetails.lastName} profile update`,
                        entityId: req.query.recipientId,
                        firstName: recipientDetails.firstName,
                        lastName: recipientDetails.lastName,
                        email: recipientDetails.email,
                        action: LOG_ACTION_TYPE.UPDATE,
                    },
                    req.user._id,
                );
                return res.handler.success(recipientDetails);
            } else {
                return res.handler.error("Recipient not updated.");
            }
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    deleteRecipient = async (req, res) => {
        try {
            const recipientDetails = await RecipientModel.findRecipientById(req.query.recipientId);
            await RecipientModel.deleteRecipientById(req.query.recipientId, req.user._id)
            await ActionLogModel.createActionLog(
                {
                    entity: 'recipient',
                    description: `${recipientDetails.firstName} ${recipientDetails.lastName} recipient deleted`,
                    entityId: req.query.recipientId,
                    firstName: recipientDetails.firstName,
                    lastName: recipientDetails.lastName,
                    email: recipientDetails.email,
                    action: LOG_ACTION_TYPE.DELETE,
                },
                req.user._id,
            );
            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

}