const ModulePermission = new (require("../Models/ModulePermission"))();
const RolesModel = new (require("../Models/RoleModel"))();

module.exports = class {
  updateRolePermission = async function (req, res, next) {
    try {
      const roleInfo = RolesModel.findRoleByFilter({
        _id: new mongoose.Types.ObjectId(req.body.role),
      });

      if(!roleInfo){
        return res.handler.badRequest("Can't find the role with given id");
      }

      if(req.body.name){
        await RolesModel.updateNewRoleById(new mongoose.Types.ObjectId(req.body.role), {
            name: req.body.name
        })
      }

      const updatedPermission = await ModulePermission.updateRolePermission(
        req.body.role,
        req.body.permissions
      );
      return res.handler.success(updatedPermission);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  getPermissionByRoles = async function (req, res, next) {
    try {
      const permissionsList = await ModulePermission.getRolesByPermission(
        req.query.roleId
      );
      return res.handler.success(permissionsList);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };
};
