const CommissionModel = new (require("../Models/CommissionModel"))();

module.exports = class {
    updateCommission = async (req, res) => {
        try{
            await CommissionModel.updateCommissionValue(req.body.id, req.body.commissionRate)
            return res.handler.success();
        }catch(err){
            return res.handler.serverError();
        }
    }

    getList = async(req, res) => {
        try{
            const charges = await CommissionModel.listCommissions();
            return res.handler.success(charges);
        }catch(err){
            return res.handler.serverError();
        }
    }
}
