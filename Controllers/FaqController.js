const FaqModel = new (require("../Models/FaqModel"))()

module.exports = class {

    listFaqs = async (req, res) => {
        try {
            const faqs = await FaqModel.findFaqs(req.query)

            if (faqs.length) {
                return res.handler.success(faqs)
            } 
            else {
                return res.handler.notFound("VALIDATION.NOT_FOUND.FAQ")
            }
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    addFaqs = async (req, res) => {
        try{
            const newlyCreatedFAQ = await FaqModel.createFaq(req.body);
            return res.handler.created(newlyCreatedFAQ);
        }catch(error){
            return res.handler.serverError(error);
        }
    }

    updateFaqs = async (req, res) => {
        try{
            const updatedFAQ = await FaqModel.updateFaq(req.body, req.params.id);
            return res.handler.updated(updatedFAQ);
        }catch(error){
            return res.handler.serverError(error);
        }
    }

    deleteFaqs = async (req, res) => {
        try{
            await FaqModel.updateFaq({
                $set:{
                    deletedAt: new Date()
                }
            }, req.params.id);
            return res.handler.success()
        }catch(error){
            return res.handler.serverError(error);
        }
    }

}