const { PDFDocument, StandardFonts, rgb } = require("pdf-lib");
const moment = require("moment");
const { DURATION_PERIOD_OPTIONS } = require("../Configs/constants");

const UserModel = new (require("../Models/UserModel"))();
const TransactionModel = new (require("../Models/TransactionsModel"))();
const MegaTechModel = new (require("../Models/MegaTechModel"))();

module.exports = class {
  getInsights = async (req, res) => {
    try {
      const {
        durationPeriod
      } = req.query

      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;
      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const countryList = await MegaTechModel.getDestinationCountries();

      const usDetails = countryList.find(
        (country) => country.CountryCode === "US"
      );

      const [
        userInsightsData,
        usToForeignTransactionsData,
        usToUsTransactionsData,
        totalTransactionsData
      ] = await Promise.all([
        UserModel.getUserCount(startDate, endDate, req.query.source, durationPeriod),
        TransactionModel.getTransactionInsight(startDate, endDate, {
          $match: {
            destinationCountryId: {
              $ne: parseInt(usDetails.countryId),
            },
          },
        }, durationPeriod),
        TransactionModel.getTransactionInsight(startDate, endDate, {
          $match: {
            destinationCountryId: parseInt(usDetails.countryId),
          },
        }, durationPeriod),
        TransactionModel.getTransactionInsight(startDate, endDate, undefined, durationPeriod),
      ]);


      let date = moment(endDate)

      const userInsights = []
      const usToForeignTransactions = []
      const usToUsTransactions = []
      const totalTransactions = []

      const units = {
        [DURATION_PERIOD_OPTIONS.DAY]: "days",
        [DURATION_PERIOD_OPTIONS.MONTH]: "months",
      }

      while (date >= startDate) {
        const year = date.year()
        const month = date.month() + 1
        const day = date.date()

        const userInsightsV = userInsightsData.find(t => {
          return t.createdYear === year &&
            t.createdMonth === month &&
            (t.createdDay === day || DURATION_PERIOD_OPTIONS.DAY !== durationPeriod)
        })

        const usToForeignTransactionsV = usToForeignTransactionsData.find(t => {
          return t.transactionYear === year &&
            t.transactionMonth === month &&
            (t.transactionDay === day || DURATION_PERIOD_OPTIONS.DAY !== durationPeriod)
        })

        const usToUsTransactionsV = usToUsTransactionsData.find(t => {
          return t.transactionYear === year &&
            t.transactionMonth === month &&
            (t.transactionDay === day || DURATION_PERIOD_OPTIONS.DAY !== durationPeriod)
        })

        const totalTransactionsV = totalTransactionsData.find(t => {
          return t.transactionYear === year &&
            t.transactionMonth === month &&
            (t.transactionDay === day || DURATION_PERIOD_OPTIONS.DAY !== durationPeriod)
        })

        userInsights.push({
          yAxis: date.format("YYYY-MM-DD"),
          xAxis: userInsightsV?.userCount ?? 0,
        })
        usToForeignTransactions.push({
          yAxis: date.format("YYYY-MM-DD"),
          xAxis: usToForeignTransactionsV?.transactionCount ?? 0,
        })
        usToUsTransactions.push({
          yAxis: date.format("YYYY-MM-DD"),
          xAxis: usToUsTransactionsV?.transactionCount ?? 0,
        })
        totalTransactions.push({
          yAxis: date.format("YYYY-MM-DD"),
          xAxis: totalTransactionsV?.transactionCount ?? 0,
        })

        date = date.subtract(1, units[durationPeriod])
      }

      return res.handler.success({
        userInsights,
        usToForeignTransactions,
        usToUsTransactions,
        totalTransactions,
      });
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  #wrapText = (text, maxWidth, font, fontSize) => {
    const words = text.split(" ");
    let lines = [];
    let currentLine = "";

    words.forEach((word) => {
      let testLine = currentLine.length > 0 ? `${currentLine} ${word}` : word;
      const textWidth = font.widthOfTextAtSize(testLine, fontSize);
      if (textWidth < maxWidth) {
        currentLine = testLine;
      } else {
        lines.push(currentLine);
        currentLine = word;
      }
    });

    lines.push(currentLine);
    return lines;
  };

  getInsightsForPDF = async (req, res) => {
    try {
      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;
      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const countryList = await MegaTechModel.getDestinationCountries();
      const usDetails = countryList.find(
        (country) => country.CountryCode === "US"
      );
      const usCountryId = parseInt(usDetails.countryId);

      const queries = [
        TransactionModel.getTransactionInsightForPDF(startDate, endDate, {
          $match: { destinationCountryId: { $ne: usCountryId } },
        }),
        TransactionModel.getTransactionInsightForPDF(startDate, endDate, {
          $match: { destinationCountryId: usCountryId },
        }),
        TransactionModel.getTransactionInsightForPDF(startDate, endDate),
      ];

      const [usToForeign, usToUs, total] = await Promise.all(queries);

      const pdfDoc = await PDFDocument.create();
      const page = pdfDoc.addPage([500, 400]);
      const { width, height } = page.getSize();
      const font = await pdfDoc.embedFont(StandardFonts.Helvetica);

      let y = height - 50;
      const fontSize = 12;
      const paddingLeft = 20;
      const paddingRight = 20;

      // Column widths: 15% for code type, 60% for transaction type, 25% for amount
      const codeTypeWidth = (width - paddingLeft - paddingRight) * 0.10; // 15% of the page width
      const labelWidth = (width - paddingLeft - paddingRight) * 0.7; // 60% of the page width
      const valueWidth = (width - paddingLeft - paddingRight) * 0.20; // 25% of the page width
      const rowHeight = 30;
      const cellPadding = 10;

      // Title
      page.drawText("Transaction Report", {
        x: width / 2 - 100,
        y,
        size: 20,
        font,
        color: rgb(0, 0, 0),
      });

      y -= 40;

      const paragraphText = "2025 Transactions (TA) V3 (Revision 0) for Amal USA Inc (113XX5)";
      const paragraphText2 = `Submitted ${moment().format("MM/DD/YYYY")} by Admin`;
      page.drawText(paragraphText, {
        x: paddingLeft,
        y: y,
        size: 12,
        font,
        color: rgb(0, 0, 0),
      });
      y -= 20;

      page.drawText(paragraphText2, {
        x: paddingLeft,
        y: y,
        size: 12,
        font,
        color: rgb(0, 0, 0),
      });

      y -= 20;

      const tableWidth = codeTypeWidth + labelWidth + valueWidth;
      const tableX = (width - tableWidth) / 2;

      let x = tableX;
      const headers = ["Code", "Transaction", "Data"];
      headers.forEach((text, index) => {
        page.drawRectangle({
          x,
          y: y - rowHeight,
          width: index === 0 ? codeTypeWidth : index === 1 ? labelWidth : valueWidth,
          height: rowHeight,
          borderColor: rgb(0, 0, 0),
          borderWidth: 1,
        });

        page.drawText(text, {
          x: x + cellPadding,
          y: y - 20,
          size: fontSize,
          font,
          color: rgb(0, 0, 0),
        });

        x += index === 0 ? codeTypeWidth : index === 1 ? labelWidth : valueWidth;
      });

      y -= rowHeight;

      // Table Data
      const tableData = [
        {
          code: "TA10",
          label: "Total # of transactions from U.S. states and territories to U.S. states and territories",
          value: String(usToUs[0]?.totalTransactionCount || 0)
        },
        {
          code: "TA20",
          label: "Total $ amount received for transmission from U.S. states and territories to U.S. states and territories",
          value: `$ ${String(usToUs[0]?.totalAmountSum || 0)}`
        },
        {
          code: "TA30",
          label: "Total # of transactions from U.S. states and territories to foreign countries",
          value: String(usToForeign[0]?.totalTransactionCount || 0)
        },
        {
          code: "TA40",
          label: "Total $ amount received for transmission from U.S. states and territories to foreign countries",
          value: `$ ${String(usToForeign[0]?.totalAmountSum || 0)}`
        },
        {
          code: "TA50",
          label: "Total # of All Money Transmission Transactions",
          value: String(total[0]?.totalTransactionCount || 0)
        },
        {
          code: "TA60",
          label: "Total $ Amount of All Money Transmission Transactions",
          value: `$ ${String(total[0]?.totalAmountSum || 0)}`
        },
      ];

      tableData.forEach((row) => {
        // Word wrap the label text
        let textLines = this.#wrapText(row.label, labelWidth - 20, font, fontSize);
        let rowHeightDynamic = Math.max(rowHeight, textLines.length * 19);  // Adjust row height based on text lines

        x = tableX;  // Reset x position for each row

        // Code Type Column
        page.drawRectangle({
          x,
          y: y - rowHeightDynamic,
          width: codeTypeWidth,
          height: rowHeightDynamic,
          borderColor: rgb(0, 0, 0),
          borderWidth: 1,
        });

        const codeX = x + (codeTypeWidth - font.widthOfTextAtSize(row.code, fontSize)) / 2;

        page.drawText(row.code, {
          x: codeX,
          y: y - 20,
          size: fontSize,
          font,
          color: rgb(0, 0, 0),
        });

        x += codeTypeWidth;

        // Transaction Type Column (Label)
        page.drawRectangle({
          x,
          y: y - rowHeightDynamic,
          width: labelWidth,
          height: rowHeightDynamic,
          borderColor: rgb(0, 0, 0),
          borderWidth: 1,
        });

        textLines.forEach((line, index) => {
          page.drawText(line, {
            x: x + cellPadding,
            y: y - (index * 15) - 15,
            size: fontSize,
            font,
            color: rgb(0, 0, 0),
          });
        });

        x += labelWidth;

        // Amount Column (Total)
        page.drawRectangle({
          x,
          y: y - rowHeightDynamic,
          width: valueWidth,
          height: rowHeightDynamic,
          borderColor: rgb(0, 0, 0),
          borderWidth: 1,
        });

        const valueX = x + (valueWidth - font.widthOfTextAtSize(row.value, fontSize)) / 2;

        page.drawText(row.value, {
          x: valueX,
          y: y - 20,
          size: fontSize,
          font,
          color: rgb(0, 0, 0),
        });

        y -= rowHeightDynamic;
      });

      const pdfBytes = await pdfDoc.save();
      res.setHeader("Content-Type", "application/pdf");
      res.setHeader("Content-Disposition", 'attachment; filename="report.pdf"');
      res.send(Buffer.from(pdfBytes));

    } catch (err) {
      return res.handler.serverError();
    }
  };

};
