const jwt = require('jsonwebtoken');

const UserModel = new (require("../Models/UserModel"))();
const UserSessionModel = new (require("../Models/UserSessionModel"))();
const ActionLogModel = new (require("../Models/ActionLogModel"))();

const encrypt = new (require("../Configs/encrypt"))()

const EmailManager = new (require("../Managers/EmailManager"))();
const SocialAuthManager = new (require("../Managers/SocialAuthManager"))();
const ModulePermission = new (require("../Models/ModulePermission"))();

const {
    LOGIN_WITH,
    LOG_ACTION_TYPE,
    USER_TYPE,
    DEVICE_TYPE
} = require("../Configs/constants");

module.exports = class {

    signIn = async (req, res) => {
        try {
            const user = await UserModel.findUserByEmail(
                req.body.email,
                undefined,
                {
                    lean: true,
                    returnConvertedUrl: true,
                    select: "+password",
                    populate: [
                        {
                            path: "roleId",
                            select: "_id name canAccessCMS slug"
                        }
                    ]
                }
            )
            if (!user)
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER")

            if (!user.password)
                return res.handler.conflict("VALIDATION.EXISTS.USER_SOCIAL")

            if (!encrypt.compareBcrypt(req.body.password, user.password))
                return res.handler.conflict("VALIDATION.PASSWORD.INCORRECT")

            if (!user.isActive)
                return res.handler.notAllowed("VALIDATION.STATUS.DEACTIVATED_ACCOUNT")

            if (
                user.roleId.canAccessCMS &&
                [
                    DEVICE_TYPE.IOS,
                    DEVICE_TYPE.ANDROID
                ].includes(req.body.deviceType)
            ) {
                return res.handler.notAllowed("VALIDATION.USER.MOBILE_NOT_ALLOW")
            }

            const newSession = await UserSessionModel.addSession(
                req.body,
                LOGIN_WITH.NORMAL,
                user._id
            );

            user.changePasswordAvailable = user.password != null
            delete user.password

            if(user.roleId.slug !== USER_TYPE.USER){
                user.permission = await ModulePermission.getRolesByPermission(user.roleId._id)
            }

            await ActionLogModel.createActionLog(
                {
                    entity: 'user',
                    description: `${user.firstName} ${user.lastName} is logged in`,
                    entityId: user._id,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    action: LOG_ACTION_TYPE.LOGIN,
                },
                user._id,
            );

            return res.handler.success({
                ...user,
                authToken: newSession.authToken,
            })
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    signUp = async (req, res) => {
        try {
            const userExist = await UserModel.findUserByEmail(req.body.email, "_id");
            req.body.userSource = req.query?.userSource ?? "web";
            if (userExist) {
                return res.handler.conflict("VALIDATION.EXISTS.EMAIL");
            }

            const newUser = (await UserModel.createUser(req.body, USER_TYPE.USER)).toObject()
            const newSession = await UserSessionModel.addSession(
                req.body,
                req.body.appleSignIn ? LOGIN_WITH.APPLE : LOGIN_WITH.NORMAL,
                newUser._id
            );

            delete newUser.password

            await ActionLogModel.createActionLog(
                {
                    entity: 'user',
                    description: `${newUser.firstName} ${newUser.lastName} is created`,
                    entityId: newUser._id,
                    firstName: newUser.firstName,
                    lastName: newUser.lastName,
                    email: newUser.email,
                    action: LOG_ACTION_TYPE.CREATE,
                },
                newUser._id,
            );

            await EmailManager.userWelcomeMail(newUser)

            return res.handler.success({
                ...newUser,
                authToken: newSession.authToken,
            });
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    socialSignIn = async (req, res) => {
        try {
            let userData

            switch (req.body.loginWith) {
                case LOGIN_WITH.GOOGLE:
                    userData = await SocialAuthManager.googleAuthVerification(req.body.token)
                    break

                case LOGIN_WITH.FACEBOOK:
                    userData = await SocialAuthManager.facebookAuthVerification(req.body.token)
                    break
				
				case LOGIN_WITH.APPLE:
					userData = await SocialAuthManager.appleAuthVerification(req.body.token)
					break
            }
            if (!userData)
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER")

            const socialIdKeyList = {
                [LOGIN_WITH.GOOGLE]: "googleId",
                [LOGIN_WITH.FACEBOOK]: "facebookId",
                [LOGIN_WITH.APPLE]: "appleId",
            }
            const socialIdKey = socialIdKeyList[req.body.loginWith]

            let user = await UserModel.findUserBySocialId(userData.id, socialIdKey)            

            if (req.body.loginWith === LOGIN_WITH.APPLE && !user) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER", {
                    id: userData.id,
                    email: userData.email,
                    firstName: userData.firstName,
                    lastName: userData.lastName
                });
            }
            
            if (!user && userData.email) {                
                user = await UserModel.findUserByEmail(userData.email, undefined, {
                    returnConvertedUrl: true,
                    populate: [
                        {
                            path: "roleId",
                            select: "_id name canAccessCMS slug",
                        },
                    ],
                })
                
                if (user) {
                    user[socialIdKey] = userData.id
                    await user.save()
                    user = user.toObject()
                }
            }

            if (!user) {
                user = (await UserModel.createUser(
                    {
                        firstName: userData.firstName,
                        lastName: userData.lastName,
                        email: userData.email,
                        [socialIdKey]: userData.id,
                        userSource: req.query?.userSource ?? "web"
                    },
                    "user"
                )).toObject()

                await EmailManager.userWelcomeMail(user)
            }

            const newSession = await UserSessionModel.addSession(
                req.body,
                req.body.loginWith,
                user._id
            )
            user.changePasswordAvailable = user.password != null
            delete user.password

            await ActionLogModel.createActionLog(
                {
                    entity: 'user',
                    description: `${user.firstName} ${user.lastName} is created`,
                    entityId: user._id,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    action: LOG_ACTION_TYPE.LOGIN,
                },
                user._id,
            );
            
            return res.handler.success({
                ...user,
                authToken: newSession.authToken,
            });
        }
        catch (err) {
            return res.handler.serverError(err)
        }
    }

    forgotPassword = async (req, res) => {
        try {
            const user = await UserModel.findUserByEmail(req.body.email, "email password");
            if (!user)
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER")

            if (!user.password)
                return res.handler.conflict("VALIDATION.EXISTS.USER_SOCIAL")

            await EmailManager.forgotPasswordMail(user)

            return res.handler.success(undefined, "PROCESS.SENT.EMAIL")
        }
        catch (err) {
            return res.handler.serverError(err)
        }
    }

    verifyForgotPassword = async (req, res) => {
        try {
            const {
                id,
            } = jwt.decode(req.query.token)

            const user = await UserModel.findUserById(id, "password", {});
            if (!user)
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER")

            try {
                jwt.verify(req.query.token, process.env.JWT_SECRET + user.password)
            }
            catch (error) {
                if (
                    error.name === 'JsonWebTokenError' &&
                    error.message === 'invalid signature'
                ) {
                    return res.handler.validationError("VALIDATION.OTP.MISMATCH")
                }
                else {
                    throw error
                }
            }

            return res.handler.success()
        }
        catch (err) {
            return res.handler.serverError(err)
        }
    }

    resetPassword = async (req, res) => {
        try {
            const {
                id,
            } = jwt.decode(req.body.token)

            const user = await UserModel.findUserById(id, "password", {});
            if (!user)
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER")

            try {
                jwt.verify(req.body.token, process.env.JWT_SECRET + user.password)
            }
            catch (error) {
                if (
                    error.name === 'JsonWebTokenError' &&
                    error.message === 'invalid signature'
                ) {
                    return res.handler.validationError("VALIDATION.OTP.MISMATCH")
                }
                else {
                    throw error
                }
            }

            user.password = req.body.password

            await Promise.all([
                user.save(),
                UserSessionModel.deleteSessions({
                    userId: user._id
                })
            ])

            return res.handler.updated(undefined, "USER.RESET_PASSWORD")
        }
        catch (err) {
            return res.handler.serverError(err)
        }
    }

};
