const UserModel = new (require("../Models/UserModel"))();
const UserSessionModel = new (require("../Models/UserSessionModel"))();
const VeriffModel = new (require("../Models/VeriffModel"))();
const VeriffDocumentModel = new (require("../Models/VeriffDocumentModel"))();
const ActionLogModel = new (require("../Models/ActionLogModel"))();
const MegaTechModel = new (require("../Models/MegaTechModel"))();

const { PATHS, VERIFICATION_STATUS, LOG_ACTION_TYPE, GENDER } = require("../Configs/constants");

const FileManager = require("../Managers/FileManager")
const EmailManager = new (require("../Managers/EmailManager"))();

const encrypt = new (require("../Configs/encrypt"))();


module.exports = class {

    getProfile = async (req, res) => {
        try {
            const user = await UserModel.getSingleUser(
                req.userId,
            )

            user.changePasswordAvailable = user.password != null
            delete user.password

            return res.handler.success(user)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    updateProfile = async (req, res) => {
        try {
            const user = await UserModel.findUserById(req.userId, undefined, {
                select: "+password",
            })

            if (!user)
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER")

            const changePasswordAvailable = user.password != null
            
            const oldProfilePicture = user.profilePicture
            if (oldProfilePicture) {
                if (
                    req.body.deleteProfilePicture ||
                    (
                        req.body.profilePicture &&
                        req.body.profilePicture.length
                    )
                ) {
                    FileManager.delete(oldProfilePicture, PATHS.USER_PROFILE, PATHS.ORIGINAL)
                    FileManager.delete(oldProfilePicture, PATHS.USER_PROFILE, PATHS.THUMB)
                    req.body.profilePicture = null
                }
            }

            if (
                req.body.profilePicture &&
                req.body.profilePicture.length
            ) {
                await FileManager.generateThumbAndUploadToCloud(req.body.profilePicture, PATHS.USER_PROFILE)
                req.body.profilePicture = req.body.profilePicture[0]
            }

            for (const key in req.body) {
                user[key] = req.body[key]
            }

            await user.save()

            if (user.profilePicture) {
                const profileUrl = FileManager.getUrl(PATHS.USER_PROFILE + PATHS.THUMB, user.profilePicture)
                user.profilePicture = profileUrl
            }

            await ActionLogModel.createActionLog(
                {
                    entity: 'user',
                    description: `${user.firstName} ${user.lastName} updated profile`,
                    entityId: req.userId,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    action: LOG_ACTION_TYPE.UPDATE,
                },
                req.userId,
            );

            delete user.password

            return res.handler.success({
                changePasswordAvailable,
                ...user.toObject()
            });
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    deleteProfile = async (req, res) => {
        try {
            await UserModel.updateUser(
                req.userId,
                {
                    deletedAt: new Date(),
                }
            );
            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    changePassword = async (req, res) => {
        try {
            const { oldPassword, password } = req.body;

            const user = await UserModel.findUser(
                {
                    _id: req.userId,
                },
                "password"
            );

            if (oldPassword === password)
                return res.handler.conflict("VALIDATION.PASSWORD.SAME");

            if (!encrypt.compareBcrypt(oldPassword, user.password))
                return res.handler.notFound("VALIDATION.PASSWORD.INCORRECT");

            user.password = password;
            await user.save();

            return res.handler.success(undefined, "USER.PASSWORD");
        }
        catch (err) {
            return res.handler.serverError(err);
        }
    };

    signOut = async (req, res) => {
        try {
            const user = req.user ?? req.admin ?? req.subAdmin
            await UserSessionModel.deleteSessions({
                authToken: req.headers.authToken,
            });

            await ActionLogModel.createActionLog(
                {
                    entity: 'user',
                    description: `${user.firstName} ${user.lastName} is logout`,
                    entityId: user._id,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    email: user.email,
                    action: LOG_ACTION_TYPE.LOGOUT,
                },
                user._id,
            );

            return res.handler.success();
        }
        catch (err) {
            return res.handler.serverError(err);
        }
    };

    createVerification = async (req, res) => {
        try {
            const user = await UserModel.findUserById(
                req.userId,
                {
                    email: 1,
                    firstName: 1,
                    lastName: 1,
                    kycStatus: 1,
                    verification: 1
                },
                {}
            )

            if (
                user.kycStatus === VERIFICATION_STATUS.PENDING ||
                user.kycStatus === VERIFICATION_STATUS.EXPIRED
            ) {
                const response = await VeriffModel.createVeriffSessions(user)

                const {
                    url,
                    sessionToken,
                    id
                } = response.verification

                user.verification = {
                    url,
                    sessionToken,
                    verificationId: id
                }
                user.kycStatus = VERIFICATION_STATUS.INITIATED

                await user.save()
            }

            return res.handler.success(user.verification)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    getMediaSession = async (req, res) => {
        try {
            const user = await UserModel.findUserById(
                req.userId,
                {
                    email: 1,
                    firstName: 1,
                    lastName: 1,
                    kycStatus: 1,
                    verification: 1
                }
            );

            if (!user || !user.verification) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.VERIFICATION_DETAILS");
            }

            const { verificationId } = user.verification;
            // const verificationId = "04d52239-18f0-4a04-996f-48dba3ffd846"

            const signature = encrypt.encryptHmac(
                verificationId,
                process.env.VERIFF_API_SECRET
            );

            const responseSession = await VeriffModel.getMediaSession(
                verificationId,
                signature.data
            );

            if (!responseSession) {
                return res.handler.serverError("VALIDATION.NOT_FOUND.VERIFICATION_DETAILS");
            }
            const documentResult = await VeriffDocumentModel.addDocument(
                responseSession,
                req.userId,
                verificationId
            );

            if (!documentResult) {
                return res.handler.serverError("VALIDATION.ERROR.MEDIA_DOCUMENT");
            }

            return res.handler.success(documentResult);

        } catch (error) {
            return res.handler.serverError(error.message || "An unexpected error occurred.");
        }
    };

    getMedia = async (req, res) => {
        try {

            const { images, videos } = req.body;

            const imageResults = [];
            const videoResults = [];

            if (images && images.length > 0) {
                for (let image of images) {
                    const signature = encrypt.encryptHmac(
                        image.id,
                        process.env.VERIFF_API_SECRET
                    );
                    const base64Data = await VeriffModel.fetchMediaDetails(
                        image.id,
                        signature.data
                    );
                    imageResults.push({ id: image.id, mimetype: image.mimetype, base64: base64Data });
                }
            }

            if (videos && videos.length > 0) {
                for (let video of videos) {
                    const signature = encrypt.encryptHmac(
                        video.id,
                        process.env.VERIFF_API_SECRET
                    );
                    const base64Data = await VeriffModel.fetchMediaDetails(
                        video.id,
                        signature.data
                    );
                    videoResults.push({ id: video.id, mimetype: video.mimetype, base64: base64Data });
                }
            }

            return res.handler.success({ images: imageResults, videos: videoResults });

        } catch (error) {
            return res.handler.serverError(error.message || "An unexpected error occurred.");
        }
    };

    verificationWebhookEvent = async (req, res) => {
        try {
            const {
                id: verificationId,
                code
            } = req.body

            const user = await UserModel.findActiveUser(
                {
                    "verification.verificationId": verificationId
                },
                {
                    kycStatus: 1
                },
                {}
            )

            if (user) {
                if (code === 7001) {
                    user.kycStatus = VERIFICATION_STATUS.STARTED
                }
                else if (code === 7002) {
                    user.kycStatus = VERIFICATION_STATUS.SUBMITTED
                }

                await user.save()
            }

            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    verificationWebhookDecision = async (req, res) => {
        try {
            const {
                id: verificationId,
                code,
                reason,
                document,
                person
            } = req.body.verification

            const user = await UserModel.findActiveUser(
                {
                    "verification.verificationId": verificationId
                },
                {
                    email: 1,
                    kycStatus: 1,
                    verification: 1,
                    gender: 1,
                    dateOfBirth: 1,
                    nationality: 1,
                    country: 1,
                    countryId: 1,
                    isEmailNotificationEnabled: 1,
                },
                {}
            )

            if (user) {
                if (code === 9001) {
                    user.kycStatus = VERIFICATION_STATUS.APPROVED
                }
                else if (code === 9102) {
                    user.kycStatus = VERIFICATION_STATUS.DECLINED
                    user.verification.reason = reason

                    if (user.isEmailNotificationEnabled) {
                        await EmailManager.userVerificationDeniedMail(user)
                    }
                }
                else if (code === 9103) {
                    user.kycStatus = VERIFICATION_STATUS.RESUBMISSION
                    user.verification.reason = reason

                    if (user.isEmailNotificationEnabled) {
                        await EmailManager.userResubmitVerificationMail(user)
                    }
                }
                else if (code === 9104) {
                    user.kycStatus = VERIFICATION_STATUS.EXPIRED
                }
                const genderValue = person.gender === "M" ? GENDER.MALE : GENDER.FEMALE;

                const countries = await MegaTechModel.getDestinationCountries();
                const matchedCountry = countries.find(country => country.CountryCode === document.country);

                user.verification.document = document
                user.verification.updated = true
                user.gender = genderValue;
                user.birthDate = person.dateOfBirth || null;
                user.nationality = person.nationality || null;
                user.country = matchedCountry?.CountryName || null;
                user.countryId = matchedCountry?.countryId || null;

                await user.save()

                const signature = encrypt.encryptHmac(
                    verificationId,
                    process.env.VERIFF_API_SECRET
                );

                const responseSession = await VeriffModel.getMediaSession(
                    verificationId,
                    signature.data
                );

                if (!responseSession) {
                    return res.handler.serverError("VALIDATION.NOT_FOUND.VERIFICATION_DETAILS");
                }

                const documentResult = await VeriffDocumentModel.addDocument(
                    responseSession,
                    user._id,
                    verificationId
                );

                if (user.kycStatus === VERIFICATION_STATUS.DECLINED) {
                    await UserModel.updateUser(user._id, {
                        deletedAt: new Date(),
                    });
                }
            }

            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

};
