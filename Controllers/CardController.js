
const UserCardModel = new (require("../Models/UserCardModel"))()
const ActionLogModel = new (require("../Models/ActionLogModel"))();

const NMIModel = new (require("../Models/NMIModel"))();

const { ADD_CUSTOMER_TOKEN, LOG_ACTION_TYPE } = require("../Configs/constants");

module.exports = class {

    listCard = async (req, res) => {
        try {
            const cards = await UserCardModel.listCardByUserId(req.user._id)

            return res.handler.success({
                cards
            })
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    createCard = async (req, res) => {
        try {
            const {
                firstName, 
                lastName,  
                email
            } = req.user

            const {
                number,
                expire,
                cvv,
            } = req.body;

            const newData = await NMIModel.createCustomerToken({
                ccnumber: number,
                ccexp: expire,
                cvv,
                customer_vault: ADD_CUSTOMER_TOKEN,
                first_name: firstName,
                last_name: lastName,
                email,
            });

            const token = newData.customer_vault_id
            if (!token) {
                const errorMessage = newData.responsetext.split(' REFID:')[0];
                return res.handler.badRequest(errorMessage);
            }

            req.body.lastDigits = number.slice(-4)

            const newCard = await UserCardModel.createCard(
                {
                    ...req.body,
                    token: newData.customer_vault_id
                },
                req.user._id
            );

            await ActionLogModel.createActionLog(
                {
                    entity: 'card',
                    description: `${firstName} ${lastName} created new card`,
                    entityId: newCard._id,
                    firstName: firstName,
                    lastName: lastName,
                    email: email,
                    action: LOG_ACTION_TYPE.CREATE,
                },
                req.user._id
            );

            return res.handler.success(newCard);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

    deleteCard = async (req, res) => {
        try {
            await UserCardModel.deleteCardById(req.query.cardId, req.user._id)
            return res.handler.success();
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    };

}