const SystemValueModel = new (require("../Models/SystemValueModel"))();
const StaticPagesModel = new (require("../Models/StaticPagesModel"))();
const TemplateModel = new (require("../Models/TemplateModel"))();

module.exports = class {

    findSystemValue = async (req, res) => {
        try {
            const value = await SystemValueModel.findValueBySlug(req.query.slug)
            return res.handler.success(value)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    updateSystemValue = async (req, res) => {
        try {
            const value = await SystemValueModel.findValueBySlug(req.body.slug, undefined, {})
            if (!value)
                return res.handler.notFound("VALIDATION.NOT_FOUND.SYSTEM.VALUE"); 

            value.title = req.body.title
            value.description = req.body.description
            value.value = req.body.value
            value.updated_by = req.user._id

            await value.save()

            return res.handler.success()

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listPages(req, res) {
        try {
            const pages = await StaticPagesModel.listPages()
            return res.handler.success(pages);
        }
        catch (err) {
            res.handler.serverError(err)
        }
    }

    async updatePage(req, res) {
        try {
            await StaticPagesModel.updatePageBySlug(req.query.slug, req.body)
            return res.handler.success()
        } catch (err) {
            res.handler.serverError(err)
        }
    }

    async getPageDetail(req, res) {
        try {
            const page = await StaticPagesModel.findPageBySlug(req.query.slug)
            return res.handler.success(page)
        }
        catch (err) {
            res.handler.serverError(err)
        }
    }

    async listTemplates(req, res) {
        try {
            const pages = await TemplateModel.listTemplates()
            return res.handler.success(pages);
        }
        catch (err) {
            res.handler.serverError(err)
        }
    }

    async updateTemplate(req, res) {
        try {
            await TemplateModel.updateTemplatesBySlug(req.query.slug, req.body)
            return res.handler.success()
        } catch (err) {
            res.handler.serverError(err)
        }
    }

    async getTemplateDetail(req, res) {
        try {
            const template = await TemplateModel.findTemplateBySlug(req.query.slug)
            return res.handler.success(template)
        }
        catch (err) {
            res.handler.serverError(err)
        }
    }



}
