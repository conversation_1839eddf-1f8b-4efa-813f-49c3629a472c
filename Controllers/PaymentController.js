const querystring = require('querystring');
const MegaTechModel = new (require("../Models/MegaTechModel"))();
const NMIModel = new (require("../Models/NMIModel"))();

const TransactionsModel = new (require("../Models/TransactionsModel"))();
const PaymentModel = new (require("../Models/PaymentModel"))();
const UserModel = new (require("../Models/UserModel"))();
const UserCardModel = new (require("../Models/UserCardModel"))();
const RecipientModel = new (require("../Models/RecipientModel"))()
const CommissionModel = new (require("../Models/CommissionModel"))()
const EmailManager = new (require("../Managers/EmailManager"))();
const ActionLogModel = new (require("../Models/ActionLogModel"))();
const CityModel = new (require("../Models/CityModel"))();

const { ADD_CUSTOMER_TOKEN, DEFAULT_CURRENCY, PAYMENT_STATUS_TYPE, LOG_ACTION_TYPE, DEFAULT_LIMIT } = require("../Configs/constants");

const {
    countryList
} = require("../Helpers/countryList");

module.exports = class NmiService {

    async createPayment(req, res) {

        try {
            const { user, body } = req;
            const { firstName, lastName, email } = user;
            const { cardId, number, expire, cvv, amount, recipientId, serviceId, stateName,
                sourceCountryName, sourceCountryId, sourceCityId, sourceCityName, payoutMethod, paymentCityName } = body;

            let transactionStatus = []
            let paymentStatus

            const [userDetails, recipientDetails, {
                finalAmount,
                finalCharge
            }, transactionLimit, isValidState, { amountToReceive, exchangeRate }] = await Promise.all([
                UserModel.findUserById(user._id, undefined, {
                    select: "+verification",
                }),
                RecipientModel.findRecipientById(recipientId),
                CommissionModel.calculateFinalCharge(amount),
                TransactionsModel.getTransactionLimit(),
                UserModel.getActiveState(stateName),
                MegaTechModel.getExchangeRateForPayment(req.body)
            ]);

            if (!amountToReceive) {
                return res.handler.notFound('VALIDATION.ERROR.EXCHANGE_RATE');
            }

            if (transactionLimit) {
                const { minLimit, maxLimit } = transactionLimit[0];
                if (parseInt(amount) < minLimit || parseInt(amount) > maxLimit) {
                    return res.handler.badRequest(`VALIDATION.ERROR.TRANSACTION_LIMIT`);
                }
            }

            if (!isValidState) {
                return res.handler.notFound('VALIDATION.ERROR.STATE_ERROR');
            }

            if (!userDetails) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.USER")
            }

            if (!recipientDetails) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.RECIPIENT")
            }

            if (finalAmount < 1) {
                return res.handler.preconditionFailed("VALIDATION.FINAL_AMOUNT.MIN")
            }

            // Step 2: Handle card creation or retrieval
            if (!cardId) {
                const customerTokenData = await NMIModel.createCustomerToken({
                    ccnumber: number,
                    ccexp: expire,
                    cvv,
                    customer_vault: ADD_CUSTOMER_TOKEN,
                    first_name: firstName,
                    last_name: lastName,
                    email,
                });

                const token = customerTokenData.customer_vault_id
                if (!token) {
                    const errorMessage = customerTokenData.responsetext.split(' REFID:')[0];
                    return res.handler.badRequest(errorMessage);
                }

                const lastDigits = String(number).slice(-4);
                const newCard = await UserCardModel.createCard(
                    {
                        ...body,
                        lastDigits,
                        token: customerTokenData.customer_vault_id,
                    },
                    user._id
                );

                await ActionLogModel.createActionLog(
                    {
                        entity: 'card',
                        description: `${firstName} ${lastName} created new card`,
                        entityId: newCard._id,
                        firstName: firstName,
                        lastName: lastName,
                        email: email,
                        action: LOG_ACTION_TYPE.CREATE,
                    },
                    user._id
                );

                body.cardId = newCard._id;
            }

            // Step 3: Retrieve the card details
            const card = await UserCardModel.findCardById(body.cardId, { token: 1, cvv: 1 });
            if (!card) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.CARD");
            }

            // Step 4: Process the payment
            const createPaymentData = await NMIModel.createPayment({
                amount: finalAmount,
                customer_vault_id: card.token,
                cvv
            },
                userDetails
            );            

            if (createPaymentData.response_code !== "100") {
                const errorMessage = createPaymentData.responsetext.split(' REFID:')[0];
                return res.handler.badRequest(errorMessage);
            }

            // Step 5: Save payment details
            const payment = await PaymentModel.createPayment(
                {
                    ...body,
                    transactionId: createPaymentData.transactionid
                },
                user._id,
                finalAmount
            );

            const refundAmount = async () => {
                const refundPaymentData = await NMIModel.refundPayment({
                    transaction_id: createPaymentData.transactionid,
                },
                    userDetails
                );
                
                if (refundPaymentData.response_code === "100") {
                    await PaymentModel.refundPayment(
                        {
                            ...body,
                            transactionId: refundPaymentData.transactionid,
                            refundReferenceTransactionId: createPaymentData.transactionid
                        },
                        user._id,
                        finalAmount
                    );
                }
            }

            let isEntryAddedInMegaTech = false

            try {
                // Step 4: Generate cash pickup transaction
                const { responseData, requestData } =
                    await MegaTechModel.generateCashPickupTransaction(
                        body,
                        payment,
                        userDetails,
                        recipientDetails,
                        finalCharge,
                        amountToReceive,
                        req.session.timezone
                    );

                if (responseData?.message && payoutMethod !== "banktransfer" && payoutMethod !== "mobilewallet") {
                    await refundAmount()
                    return res.handler.preconditionFailed(responseData.message)
                }

                // Step 5: Save the transaction
                const newTransaction = await TransactionsModel.createTransaction(
                    recipientId,
                    body.cardId,
                    requestData,
                    responseData,
                    user._id,
                    payment._id,
                    finalAmount,
                    exchangeRate,
                    stateName,
                    sourceCountryName,
                    sourceCountryId,
                    sourceCityName,
                    sourceCityId,
                    paymentCityName,
                );
                isEntryAddedInMegaTech = true

                transactionStatus.push({
                    status: PAYMENT_STATUS_TYPE.INITIATED,
                    date: new Date()
                })
                paymentStatus = PAYMENT_STATUS_TYPE.INITIATED

                if (userDetails.isEmailNotificationEnabled) {
                    await EmailManager.userPaymentStatusMail(email, responseData.TransactionId, PAYMENT_STATUS_TYPE.INITIATED);
                }

                if (payoutMethod !== "banktransfer" && payoutMethod !== "mobilewallet") {
                    await MegaTechModel.confirmTransaction(newTransaction.transactionRefNo);
                }

                transactionStatus.push({
                    status: PAYMENT_STATUS_TYPE.PROCESSING,
                    date: new Date().toISOString(),
                })
                paymentStatus = PAYMENT_STATUS_TYPE.PROCESSING
                if (userDetails.isEmailNotificationEnabled) {
                    await EmailManager.userPaymentStatusMail(email, responseData.TransactionId, PAYMENT_STATUS_TYPE.PROCESSING);
                }

                newTransaction.transactionStatus = transactionStatus
                newTransaction.paymentStatus = paymentStatus
                await newTransaction.save()

                await ActionLogModel.createActionLog(
                    {
                        entity: 'transaction',
                        description: `${userDetails.firstName} ${userDetails.lastName} created new payment`,
                        entityId: newTransaction._id,
                        firstName: userDetails.firstName,
                        lastName: userDetails.lastName,
                        email: userDetails.email,
                        action: LOG_ACTION_TYPE.CREATE,
                    },
                    user._id,
                );

                return res.handler.success(newTransaction, "VALIDATION.SUCCESS.PAYMENT");
            } catch (error) {                          
                if (!isEntryAddedInMegaTech) {
                    await refundAmount()
                }
                throw error
            }
        } catch (error) {
            try {
                const errorResponse = JSON.parse(error.message)
                if (errorResponse) {
                    const firstErrorKey = Object.keys(errorResponse)[0];
                    const firstErrorMessage = errorResponse[firstErrorKey][0];
                    console.log(firstErrorMessage);
                    return res.handler.preconditionFailed(firstErrorMessage)
                }
                const errorDetails = error.response
                    ? querystring.parse(error.response.data)
                    : error.message;
                return res.handler.serverError(errorDetails, "VALIDATION.ERROR.PAYMENT");
            } catch (err) {
                return res.handler.serverError(error);
            }
        }
    }

    async paymentWebhookEvent(req, res) {
        try {
            const { amaltransactionId, status } = req.body;

            const transaction = await TransactionsModel.findTransactionByTransactionId(amaltransactionId);
            if (!transaction) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.TRANSACTION");
            }

            if (status === "PAID") {
                const userDetails = await UserModel.findUserById(transaction.userId, undefined, { select: "+verification" });
                if (!userDetails) {
                    return res.handler.notFound("VALIDATION.NOT_FOUND.USER");
                }

                transaction.transactionStatus.push({
                    status: PAYMENT_STATUS_TYPE.COMPLETED,
                    date: new Date().toISOString(),
                });
                transaction.paymentStatus = PAYMENT_STATUS_TYPE.COMPLETED;
                if (userDetails.isEmailNotificationEnabled) {
                    await EmailManager.userPaymentStatusMail(userDetails.email, transaction.transactionId, PAYMENT_STATUS_TYPE.COMPLETED);
                }

                await ActionLogModel.createActionLog({
                    entity: "transaction",
                    description: `${userDetails.firstName} ${userDetails.lastName} updated payment status`,
                    entityId: transaction._id,
                    firstName: userDetails.firstName,
                    lastName: userDetails.lastName,
                    email: userDetails.email,
                    action: LOG_ACTION_TYPE.UPDATE,
                }, userDetails._id);
            }

            await transaction.save();

            return res.handler.success({
                amaltransactionId: transaction.transactionId,
                amalPayReferenceNo: transaction.transactionReferenceNo,
                paymentStatus: transaction.paymentStatus,
            });
        } catch (error) {
            return res.handler.serverError(error);
        }
    };


    async trackTransaction(req, res) {
        try {
            const { transactionId } = req.body;

            const transaction = await TransactionsModel.findTransactionByFilter(
                {
                    $or: [
                        {
                            transactionRefNo: transactionId,
                        },
                        {
                            transactionId,
                        }
                    ]
                }
            );
            if (!transaction) {
                return res.handler.notFound("VALIDATION.NOT_FOUND.TRANSACTION")
            }
            return res.handler.success(transaction);
        }
        catch (error) {
            return res.handler.serverError(error.message || 'VALIDATION.ERROR.MEDIA');
        }
    }

    async listCountries(req, res) {
        try {
            // const countries = await MegaTechModel.getDestinationCountries();
            // return res.handler.success(countries);
            return res.handler.success(countryList);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listCity(req, res) {
        try {
            const countries = await MegaTechModel.getCities(req.query.countryId);
            return res.handler.success(countries);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getExchangeRate(req, res) {
        try {
            const exchangeRate = await MegaTechModel.getExchangeRate(req.body);
            return res.handler.success(exchangeRate);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listState(req, res) {
        try {
            const searchValue = req.query?.searchValue;
            const limit = parseInt(req.query.limit ?? DEFAULT_LIMIT);
            const skip = ((req.query.page ?? 1) - 1) * limit;
            const states = await UserModel.getStates(
                skip,
                limit,
                searchValue
            );
            return res.handler.success(states);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async toggleStateStatus(req, res) {
        try {
            await UserModel.toggleStateStatus(
                {
                    _id: req.query.stateId
                },
                req.body
            );
            return res.handler.success();
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async validStateCheck(req, res) {
        try {
            const { stateName, cityName } = req.body
            const isValidState = await UserModel.getActiveState(stateName);

            let city
            if (cityName) {
                city = await CityModel.findCityByName(cityName)
            }

            if (isValidState) {
                return res.handler.success({
                    city
                });
            } else {
                return res.handler.notFound('VALIDATION.ERROR.STATE_ERROR');
            }
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async checkRecipient(req, res) {
        try {
            const {
                serviceId,
                serviceOperatorId,
                recipientId,
                checkAccount
            } = req.query

            const recipient = await RecipientModel.findRecipientById(recipientId, undefined, {
                lean: true
            })
            if (!recipient)
                return res.handler.notFound("VALIDATION.NOT_FOUND.RECIPIENT")

            const firstName = recipient.firstName.toLowerCase()

            let mobileVerified
            let accountVerified

            const mobileVerification = await MegaTechModel.verifyMobileNumber({
                serviceId,
                serviceOperatorId,
                beneficiaryMobileCountryCode: parseInt(recipient.countryCode),
                beneficiaryMobile: recipient.phoneNumber,
            })
            mobileVerified = mobileVerification.BeneficiaryName?.split(" ")[0].toLowerCase() === firstName

            if (recipient.accountNumber && checkAccount) {
                const accountVerification = await MegaTechModel.verifyBankAccount({
                    serviceId,
                    serviceOperatorId,
                    beneficiaryBankAccNo: recipient.accountNumber,
                })
                accountVerified = accountVerification.BeneficiaryName?.split(" ")[0].toLowerCase() === firstName
            }

            return res.handler.success({
                isVerified: mobileVerified && (checkAccount ? accountVerified : true),
            });
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listServices(req, res) {
        try {
            const services = await MegaTechModel.getServices(req.query.countryId);
            const data = []

            for (let i = 0; i < services.length; i++) {
                const service = services[i];
                service.payoutMethod = service.ServiceName.toLowerCase().replace(/\s/g, '')

                if (req.query.includeOperators) {
                    const serviceOperators = await MegaTechModel.getServiceOperators(req.query.countryId, service.ServiceId)
                    service.serviceOperators = serviceOperators
                }

                data.push(service)
            }

            return res.handler.success(data);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCharges(req, res) {
        try {
            const charges = await CommissionModel.getChargesInObject()
            return res.handler.success(charges);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listPricing(req, res) {
        try {
            const amount = parseFloat(req.query.amount)

            let getExchangeRate = null;
            if (req.query.currency) {
                getExchangeRate = await MegaTechModel.getExchangeRate(req.query);
            }

            let exchangeRate = 1;
            if (getExchangeRate) {
                exchangeRate = parseFloat(getExchangeRate.ExchangeRate);
                getExchangeRate.convertedAmount = (amount * exchangeRate).toFixed(2);
            }

            const [
                services,
                charges,
            ] = await Promise.all([
                MegaTechModel.getServices(req.query.countryId),
                CommissionModel.getChargesInObject(),
            ])

            const {
                finalAmount,
                finalCharge
            } = await CommissionModel.calculateFinalCharge(amount, charges)

            const pricing = services.map(service => {
                return {
                    serviceId: service.ServiceId,
                    deliveryMethod: service.ServiceName,
                    fees: finalCharge,
                    currency: DEFAULT_CURRENCY,
                    amount,
                }
            })

            return res.handler.success({
                pricing,
                charges,
                getExchangeRate
            });

        } catch (error) {
            return res.handler.serverError(error);
        }
    }
};
