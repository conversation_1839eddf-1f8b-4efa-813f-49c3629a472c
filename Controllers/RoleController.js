const slugify = require("slugify");
const { USER_TYPE } = require("../Configs/constants");
const { default: mongoose } = require("mongoose");
const RoleModel = new (require("../Models/RoleModel"))();
const ModulePermission = new (require("../Models/ModulePermission"))();
const UserModel = new (require("../Models/UserModel"))();

module.exports = class {
  getAllRoles = async (req, res, next) => {
    try {
      const roleList = await RoleModel.getAllRoles({
        canAccessCMS: true,
        deletedAt: {
          $exists: false,
        },
      });
      return res.handler.success(roleList);
    } catch (err) {
      return res.handler.serverError();
    }
  };

  addRole = async (req, res, next) => {
    const session = await mongoose.startSession();
    try {

      const isRoleExists = await RoleModel.findRoleBySlug(slugify(req.body.name.trim()))
      if(isRoleExists?._id){
        return res.handler.badRequest("Role name must be unique");
      }
      session.startTransaction();
      const newlyCreateRole = await RoleModel.addNewRole({
        name: req.body.name,
        slug: slugify(req.body.name),
        canAccessCMS: true,
      });

      const permissionList = req.body.permissions.map((permission) => ({
        ...permission,
        name: permission.name,
        role: newlyCreateRole._id,
        canAccessCMS: true,
      }));

      await ModulePermission.insertMany(permissionList);
      await session.commitTransaction();
      session.endSession();

      return res.handler.success();
    } catch (err) {
      await session.abortTransaction();
      session.endSession();
      console.log("error message", err.message);
      return res.handler.serverError();
    }
  };

  deleteRole = async (req, res, next) => {
    try {
      const roleInfo = await RoleModel.findRoleByFilter({
        _id: new mongoose.Types.ObjectId(req.params.id),
        deletedAt: {
          $exists: false,
        },
      });

      if (!roleInfo?._id)
        return res.handler.badRequest("Can't find the role with this id");

      if(!roleInfo.isModify)
        return res.handler.badRequest("This role can't be deleted");

      await RoleModel.updateNewRoleById(
        new mongoose.Types.ObjectId(req.params.id),
        {
          $set: {
            deletedAt: new Date(),
          },
        }
      );

      await ModulePermission.updateModules(
        {
          role: new mongoose.Types.ObjectId(req.params.id),
        },
        {
          $set: {
            deletedAt: new Date(),
          },
        }
      );

      const userRoleInfo = await RoleModel.findRoleBySlug(USER_TYPE.USER);

      await UserModel.bulkUpdate(
        {
          roleId: roleInfo._id,
        },
        {
          $set: {
            roleId: userRoleInfo._id,
          },
        }
      );

      return res.handler.success();
    } catch (err) {
      return res.handler.serverError();
    }
  };
};
