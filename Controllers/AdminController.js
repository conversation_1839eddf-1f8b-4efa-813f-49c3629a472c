const moment = require("moment");
const {
  DEFAULT_LIMIT,
  PATHS,
  USER_IMPORT_LIMIT,
  LOG_ACTION_TYPE,
  USER_TYPE,
  VERIFICATION_STATUS
} = require("../Configs/constants");
const FileManager = require("../Managers/FileManager");
const EmailManager = new (require("../Managers/EmailManager"))();

const UserModel = new (require("../Models/UserModel"))();
const ActionLogModel = new (require("../Models/ActionLogModel"))();
const RoleModel = new (require("../Models/RoleModel"))();
const VeriffDocumentModel = new (require("../Models/VeriffDocumentModel"))();
const UserSessionModel = new (require("../Models/UserSessionModel"))();
const MegaTechModel = new (require("../Models/MegaTechModel"))();

const csv = require("csv-parser");

const fastCsv = require("fast-csv");
const fs = require("fs");
const path = require("path");
const mongoose = require("mongoose");

module.exports = class {
  generateRandomString = () => {
    const length = Math.floor(Math.random() * (14 - 6 + 1)) + 6;
    const characters =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let result = "";

    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * characters.length);
      result += characters[randomIndex];
    }

    return (
      result +
      Math.ceil(Math.random() * 10) +
      characters[Math.floor(Math.random() * (25 - 0 + 1))] +
      characters[Math.floor(Math.random() * (51 - 26 + 1)) + 26]
    );
  };

  listUsers = async (req, res) => {
    try {
      const limit = parseInt(req.query.limit ?? DEFAULT_LIMIT);
      const skip = ((req.query.page ?? 1) - 1) * limit;
      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;

      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const filterValue = req.query?.filterValue;
      const sortKey = req.query?.sortBy ?? "createdAt";

      const order = req.query?.order?.toLowerCase() === "asc" ? 1 : -1;

      const users = await UserModel.getAllUser(
        skip,
        limit,
        startDate,
        endDate,
        filterValue,
        sortKey,
        order,
        req.query?.state,
        req.query?.city,
        req.query?.userSource
      );

      users.users.forEach((user) => {
        if (user.profilePicture)
          user.profilePicture = FileManager.getUrl(
            PATHS.USER_PROFILE + PATHS.THUMB,
            user.profilePicture
          );
      });

      return res.handler.success(users);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  listSubAdmin = async (req, res) => {
    try {
      const limit = parseInt(req.query.limit ?? DEFAULT_LIMIT);
      const skip = ((req.query.page ?? 1) - 1) * limit;
      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;

      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const filterValue = req.query?.filterValue;
      const sortKey = req.query?.sortBy ?? "updatedAt";

      const order = req.query?.order?.toLowerCase() === "asc" ? 1 : -1;
      const adminInfo = req.admin ?? req.subAdmin;

      const subAdmin = await UserModel.getAllSubAdmin(
        skip,
        limit,
        startDate,
        endDate,
        filterValue,
        sortKey,
        order,
        req.query?.state,
        req.query?.city,
        req.query?.userSource,
        adminInfo
      );

      subAdmin.users.forEach((user) => {
        if (user.profilePicture)
          user.profilePicture = FileManager.getUrl(
            PATHS.USER_PROFILE + PATHS.THUMB,
            user.profilePicture
          );
      });

      return res.handler.success(subAdmin);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  // user type is slug for the users and mongodb
  createUserWithRoles = async (req, res, userType) => {
    const userExist = await UserModel.findUserByEmail(req.body.email, "_id");

    if (userExist) {
      return res.handler.conflict("VALIDATION.EXISTS.EMAIL");
    }

    req.body.password = this.generateRandomString();
    req.body.createdBy = req.admin._id
      ? new mongoose.Types.ObjectId(req.admin._id)
      : new mongoose.Types.ObjectId(req.subAdmin._id);
    req.body.userSource = req.body?.userSource ?? "web";

    const role = userType === USER_TYPE.USER ? await RoleModel.findRoleBySlug(userType) : await RoleModel.findRoleByFilter({
      _id: new mongoose.Types.ObjectId(userType)
    });

    const newlyCreateUser = await UserModel.addUser(req.body, role);

    await EmailManager.temporaryPasswordMail(
      newlyCreateUser,
      req.body.password
    );

    if (userType === USER_TYPE.USER) {
      await VeriffDocumentModel.addAdminCreatedUserDocument(
        newlyCreateUser._id,
        req.body
      );

      await FileManager.uploadToCloud(
        req.body.files,
        `${PATHS.USER_DOC_VERIFIED_ADMIN}/${newlyCreateUser._id}`
      );
    }

    if (req.body.profilePicture) {
      await FileManager.generateThumbAndUploadToCloud(
        req.body.profilePicture,
        PATHS.USER_PROFILE
      );
      await UserModel.updateUser(newlyCreateUser._id, {
        profilePicture: req.body.profilePicture[0],
      });
    }

    await ActionLogModel.createActionLog(
      {
        entity: userType === USER_TYPE.USER ? "user" : "sub admin",
        description: `${newlyCreateUser.firstName} ${newlyCreateUser.lastName
          } ${userType === USER_TYPE.USER ? "user" : "sub admin"} is created`,
        entityId: newlyCreateUser._id,
        firstName: newlyCreateUser.firstName,
        lastName: newlyCreateUser.lastName,
        email: newlyCreateUser.email,
        action: LOG_ACTION_TYPE.CREATE,
      },
      newlyCreateUser._id
    );

    return res.handler.success(newlyCreateUser);
  };

  addUser = async (req, res) => {
    try {
      await this.createUserWithRoles(req, res, USER_TYPE.USER);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  addSubAdmin = async (req, res) => {
    try {
      await this.createUserWithRoles(req, res, req.body.role);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  downloadDocument = async (req, res) => {
    try {
      const downloadDocs = await VeriffDocumentModel.createDocumentDownloadList(
        req.body.userId
      );

      const downloadLinkList = downloadDocs.map((doc) =>
        FileManager.getUrl(
          `${PATHS.USER_DOC_VERIFIED_ADMIN}/${req.body.userId}`,
          doc?.documentName
        )
      );
      return res.handler.success(downloadLinkList);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  editUserByRole = async (req, res, userType) => {
    try {
      const userRole = await UserModel.findActiveUser(
        {
          _id: new mongoose.Types.ObjectId(req.params.userId),
          deletedAt: {
            $exists: false
          }
        },
        {},
        {},
        true
      );

      if (USER_TYPE.USER === userType && userRole?.roleId?.slug !== userType) {
        return res.handler.badRequest("VALIDATION.USER.INVALID_USER_ROLE");
      }

      if (req.body.role) {
        if (mongoose.isValidObjectId(req.body.role.trim())) {
          const isRoleIdValid = await RoleModel.findRoleByFilter({
            _id: new mongoose.Types.ObjectId(req.body.role.trim())
          });
          if (!isRoleIdValid)
            return res.handler.badRequest("VALIDATION.USER.INVALID_ROLE_ID");
          req.body.roleId = new mongoose.Types.ObjectId(req.body.role.trim());
        }
        else {
          return res.handler.badRequest("VALIDATION.USER.INVALID_ROLE_ID");
        }
      }

      if (req.body.email) {
        const isEmailExists = await UserModel.findActiveUser(
          {
            email: req.body.email,
            deletedAt: {
              $exists: false
            }
          },
          {},
          {},
          true
        );

        if (isEmailExists && isEmailExists?._id.toString() !== userRole?._id.toString()) {
          return res.handler.badRequest("VALIDATION.USER.EMAIL_ALREADY_EXISTS");
        }
      }


      let profilePicture;
      if (req.body.profilePicture) {
        profilePicture = req.body.profilePicture;
        delete req.body.profilePicture;
      }
      const user = await UserModel.updateUser(req.params.userId, req.body);

      if (req.body.role) {
        await EmailManager.userRoleUpdate(req.body.email)
      }

      if (userType === USER_TYPE.USER) {
        if (req.body.deletedList) {
          await VeriffDocumentModel.deletedDocument(
            req.body.deletedList,
            req.params.userId
          );

          // if (moveToTrashDocs) await Promise.all(moveToTrashDocs);
        }

        if (req.body.files) {
          await VeriffDocumentModel.addAdminCreatedUserDocument(
            req.params.userId,
            req.body
          );
          await FileManager.uploadToCloud(
            req.body.files,
            `${PATHS.USER_DOC_VERIFIED_ADMIN}/${req.params.userId}`
          );
          await UserModel.updateUser(
            req.params.userId,
            {
              kycStatus: VERIFICATION_STATUS.APPROVED
            }
          );
        }

        if (!req.body.files && !req.body.deletedLis) {
          await VeriffDocumentModel.updateAdminCreatedUserDocument(
            req.params.userId,
            req.body
          );
        }
      }


      if (profilePicture) {
        await FileManager.generateThumbAndUploadToCloud(
          profilePicture,
          PATHS.USER_PROFILE
        );

        await UserModel.updateUser(user._id, {
          profilePicture: profilePicture[0],
        });

        if (user.profilePicture) {
          FileManager.delete(
            user.profilePicture,
            PATHS.USER_PROFILE,
            PATHS.ORIGINAL
          );
          FileManager.delete(
            user.profilePicture,
            PATHS.USER_PROFILE,
            PATHS.THUMB
          );
        }
      }

      res.handler.success(user, "USER.UPDATED");
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  editUser = async (req, res) => {
    try {
      await this.editUserByRole(req, res, USER_TYPE.USER);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  getUserInfoById = async (req, res) => {
    try {
      const userInfo = (await UserModel.getUserId(req.params.userId));

      if (userInfo.profilePicture) {
        userInfo.profilePicture = FileManager.getUrl(
          PATHS.USER_PROFILE + PATHS.THUMB,
          userInfo.profilePicture
        );
      }

      return res.handler.success(userInfo)
    } catch (err) {
      return res.handler.serverError(err);
    }
  }

  editSubAdmin = async (req, res) => {
    try {
      await this.editUserByRole(req, res, req.body.role);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  importUser = async (req, res) => {
    try {
      const users = [];
      const emails = [];
      const countryCodePattern =
        /^[+]{1}(?:[0-9\-\\(\\)\\/.]\s?){6,15}[0-9]{1}$/;
      const phoneNumberPattern = /^[0-9]*$/;
      const emailValidationPattern =
        /\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;
      const passwordGenerator = this.generateRandomString;
      const countries = await MegaTechModel.getDestinationCountries();
      fs.createReadStream(req.file.path)
        .pipe(csv())
        .on("data", (row) => {
          if (Object.keys(row).length) {
            emails.push(row.email);
            users.push({
              ...row,
              isUserImported: true,
              isCreatedByAdmin: true,
              password: passwordGenerator(),
            });
          }
        })
        .on("end", async () => {
          try {
            if (users.length > USER_IMPORT_LIMIT) {
              return res.handler.badRequest(
                `Can't import more than ${USER_IMPORT_LIMIT}`
              );
            }

            const role = await RoleModel.findRoleBySlug(USER_TYPE.USER, "name");

            let listOfExistingEmail = await UserModel.getUserEmailList(emails);
            listOfExistingEmail = listOfExistingEmail.map(
              (value) => value.email
            );

            const filterUserList = [];
            const mailList = [];
            const errors = [];
            users.forEach((user, index) => {
              console.log("🚀 ~ users.forEach ~ user:", user)
              if (
                !user.firstName ||
                (user.firstName && user.firstName.trim().length < 2)
              ) {
                errors.push({
                  row: index + 1,
                  reason:
                    "First name is either missing or it must be more than 2 characters",
                });
              } else if (
                !user.lastName ||
                (user.lastName && user.lastName.trim().length < 2)
              ) {
                errors.push({
                  row: index + 1,
                  reason:
                    "Last name is either missing or it must be more than 2 characters",
                });
              } else if (
                !user.countryCode ||
                (user.countryCode && countryCodePattern.test(user.countryCode))
              ) {
                errors.push({
                  row: index + 1,
                  reason:
                    "Country code is either missing or it is invalid format",
                });
              } else if (!user.countryShortName) {
                errors.push({
                  row: index + 1,
                  reason: "Country Shortname is required"
                });
              } else if (!countries.find(country => country.CountryCode === user?.countryShortName.trim())) {
                errors.push({
                  row: index + 1,
                  reason: "Please enter valid country short name from the sample csv"
                });
              }
              else if (
                !user.phoneNumber ||
                (user.phoneNumber &&
                  phoneNumberPattern.test(user.phoneNumber) &&
                  user.phoneNumber.trim() < 12)
              ) {
                errors.push({
                  row: index + 1,
                  reason:
                    "Phone number is either missing or it is invalid format",
                });
              } else if (
                !user.address ||
                user.address.trim().length < 2
              ) {
                errors.push({
                  row: index + 1,
                  reason:
                    "Address is either missing or it has less than 2 character format",
                });
              } else if (!user.birthDate) {
                errors.push({
                  row: index + 1,
                  reason:
                    "Birth date is either missing or it has less than 3 character format",
                });
              } else if (!user.zipCode || user.zipCode.trim().length < 5) {
                errors.push({
                  row: index + 1,
                  reason:
                    "Zip code is either missing or it has less than 5 character format",
                });
              } else if (
                !user.email ||
                !emailValidationPattern.test(user.email.trim())
              ) {
                errors.push({
                  row: index + 1,
                  reason: "Email is either missing or not valid email",
                });
              } else if (
                !user.gender ||
                !["male", "female"].includes(user.gender.trim().toLowerCase())
              ) {
                errors.push({
                  row: index + 1,
                  reason: "Gender is either missing or not have valid value",
                });
              } else {
                if (!listOfExistingEmail.includes(user.email.trim())) {
                  console.log("hello world")
                  user.countryId = countries.find(country => country.CountryCode === user?.countryShortName.trim()).countryId;
                  user.country = countries.find(country => country.CountryCode === user?.countryShortName).CountryName;
                  console.log("-------", user)
                  filterUserList.push({
                    ...user,
                    roleId: role._id,
                    gender: user.gender.trim().toUpperCase(),
                    createdBy: req.admin._id,
                  });
                  mailList.push(
                    EmailManager.temporaryPasswordMail(
                      { email: user.email },
                      user.password
                    )
                  );
                } else {
                  errors.push({
                    row: index + 1,
                    reason: "Email already exists",
                  });
                }
              }
            });

            if (filterUserList.length) {
              await UserModel.importUser(filterUserList);
            }

            res.handler.success(
              {
                errors: errors.length && errors,
              },
              `${filterUserList.length}/${users.length} users imported successfully`
            );

            if (mailList.length) await Promise.allSettled(mailList);
          } catch (err) {
            console.error("Error inserting users into MongoDB:", err);
            res.handler.serverError("Failed to import users");
          } finally {
            fs.unlink(req.file.path, function (err) {
              if (err) console.log(err);
            });
          }
        });
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  exportCsv = async (req, res) => {
    try {
      res.setHeader("Content-Type", "text/csv");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="users_export.csv"`
      );

      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;

      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const cursor = await UserModel.exportUser(
        startDate,
        endDate,
        req.query.filterValue,
        req.query.sortKey,
        req.query.order,
        req.query?.state,
        req.query?.city,
        req.query?.userSource,
        req.query?.timeZone
      );

      const csvStream = fastCsv.format({ headers: true });

      csvStream.pipe(res);
      cursor.on("data", (user) => {
        csvStream.write(user);
      });

      cursor.on("end", () => {
        csvStream.end();
        console.log("CSV export completed.");
      });

      cursor.on("error", (err) => {
        console.error("Error exporting CSV:", err.message);
        res.status(500).send("Failed to export CSV.");
      });
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  getUserSource = async (req, res) => {
    try {
      const sources = await UserModel.getUniqueSources();
      return res.handler.success(sources);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  findActionLogLists = async (req, res) => {
    try {
      const limit = parseInt(req.query.limit ?? DEFAULT_LIMIT);
      const skip = ((req.query.page ?? 1) - 1) * limit;
      const startDate = req.query.startDate
        ? moment(req.query.startDate, "YYYY-MM-DD")
        : null;

      const endDate = req.query.endDate
        ? moment(req.query.endDate, "YYYY-MM-DD")
        : null;

      const filterValue = req.query?.filterValue;

      const logs = await ActionLogModel.findActionLogLists(
        skip,
        limit,
        startDate,
        endDate,
        filterValue
      );
      return res.handler.success(logs);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  findActionLogEntityLists = async (req, res) => {
    try {
      const logsEntity = await ActionLogModel.findActionLogEntityLists();
      return res.handler.success(logsEntity);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  deleteUserByRole = async (req, res, userType) => {
    const userRole = await UserModel.findActiveUser(
      {
        _id: new mongoose.Types.ObjectId(req.body.userId),
      },
      {},
      {},
      true
    );
    const roleId = userType === USER_TYPE.USER === userType ? await RoleModel.findRoleByFilter({
      _id: new mongoose.Types.ObjectId(req.body.role)
    }) : await RoleModel.findRoleBySlug(USER_TYPE.USER)

    if (!roleId) {
      return res.handler.badRequest("VALIDATION.USER.INVALID_USER_ROLE");
    }

    if (USER_TYPE.USER === userType && userRole?.roleId?.slug !== userType) {
      return res.handler.badRequest("VALIDATION.USER.INVALID_USER_ROLE");
    }

    await UserModel.updateUser(req.body.userId, {
      deletedAt: new Date(),
    });

    return res.handler.success();
  };

  deactivateUser = async (req, res) => {
    try {
      await this.deleteUserByRole(req, res, USER_TYPE.USER);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  deactivateAdmin = async (req, res) => {
    try {
      await this.deleteUserByRole(req, res, req.body.role);
    } catch (err) {
      return res.handler.serverError(err);
    }
  };

  demoCsvDownload = (req, res) => {
    try {
      const filePath = path.join(
        __dirname,
        "../public",
        "demo_user_import.csv"
      );

      res.download(filePath, "demo_user_import.csv", (err) => {
        if (err) {
          console.error("Error sending file:", err);
        }
      });
    } catch (err) {
      return res.handler.serverError();
    }
  };

  resetPassword = async (req, res) => {
    try {
      const user = await UserModel.findUserById(req.body.userId, "password", {});
      if (!user)
        return res.handler.notFound("VALIDATION.NOT_FOUND.USER")

      user.password = req.body.password

      await Promise.all([
        user.save(),
        UserSessionModel.deleteSessions({
          userId: req.body.userId
        })
      ])

      return res.handler.updated(undefined, "USER.RESET_PASSWORD")
    }
    catch (err) {
      return res.handler.serverError(err)
    }
  }

};
