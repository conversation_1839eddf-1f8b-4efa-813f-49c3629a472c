{"STATUS": {"1XX_INFORMATIONAL": "", "CONTINUE": 100, "SWITCHING_PROTOCOLS": 101, "PROCESSING": 102, "EARLY_HINTS": 103, "2XX_SUCCESS": "", "SUCCESS": "Success", "CREATED": "Created Successfully", "ACCEPTED": 202, "NON_AUTHORITATIVE_INFORMATION": 203, "NO_CONTENT": 204, "RESET_CONTENT": 205, "PARTIAL_CONTENT": 206, "MULTI_STATUS": 207, "ALREADY_REPORTED": 208, "IM_USED": 226, "3XX_REDIRECTION": "", "MULTIPLE_CHOICES": 300, "MOVED_PERMANENTLY": 301, "FOUND": 302, "SEE_OTHER": 303, "NOT_MODIFIED": 304, "USE_PROXY": 305, "TEMPORARY_REDIRECT": 307, "PERMANENT_REDIRECT": 308, "4XX_CLIENT_ERROR": "", "BAD_REQUEST": 400, "UNAUTHORIZED": "You are not authorized to access this location.", "PAYMENT_REQUIRED": 402, "FORBIDDEN": "Forbidden Error !", "NOT_FOUND": "Requested resource not found !", "NOT_ALLOWED": "Method is not allowed !", "NOT_ACCEPTABLE": 406, "PROXY_AUTHENTICATION_REQUIRED": 407, "REQUEST_TIMEOUT": 408, "CONFLICT": "Provided information already exist!", "GONE": 410, "LENGTH_REQUIRED": 411, "PRECONDITION_FAILED": "Please complete other steps first", "PAYLOAD_TOO_LARGE": 413, "URI_TOO_LONG": 414, "UNSUPPORTED_MEDIA_TYPE": 415, "RANGE_NOT_SATISFIABLE": 416, "EXPECTATION_FAILED": 417, "UNPROCESSABLE_ENTITY": 422, "VALIDATION_ERROR": "Validation error !", "NOT_VALID_DATA": "Data is understood, but is still not valid. !", "LOCKED": 423, "FAILED_DEPENDENCY": 424, "UNORDERED_COLLECTION": 425, "UPGRADE_REQUIRED": 426, "PRECONDITION_REQUIRED": 428, "TOO_MANY_REQUESTS": 429, "REQUEST_HEADER_FIELDS_TOO_LARGE": 431, "UNAVAILABLE_FOR_LEGAL_REASONS": 451, "5XX_SERVER_ERROR": "", "SERVER_ERROR": "Internal Server Error ! Please try again later", "NOT_IMPLEMENTED": 501, "BAD_GATEWAY": 502, "SERVICE_UNAVAILABLE": 503, "GATEWAY_TIMEOUT": 504, "HTTP_VERSION_NOT_SUPPORTED": 505, "VARIANT_ALSO_NEGOTIATES": 506, "INSUFFICIENT_STORAGE": 507, "LOOP_DETECTED": 508, "BANDWIDTH_LIMIT_EXCEEDED": 509, "NOT_EXTENDED": 510, "NETWORK_AUTHENTICATION_REQUIRED": 511}, "VALIDATION": {"SUCCESS": {"PAYMENT": "Payment successfully created"}, "ERROR": {"PAYMENT": "Payment successfully created", "TRANSACTION": "An error occurred while tracking the transaction", "MEDIA": "Failed to retrieve media session data", "MEDIA_DOCUMENT": "Failed to add verification document.", "STATE_ERROR": "Payment processing is restricted to your registered state.", "TRANSACTION_LIMIT": "Transaction amount is not allowed. Please ensure the amount is valid."}, "EXISTS": {"USER": "User already exist!", "USER_SOCIAL": "User have registered by social account, Please try logging with same!", "EMAIL": "An account with this email address already exists. Please use a different email address", "PHONE": "Phone is already registered, Please try logging in!", "USER_NAME": "Username is already registered!"}, "NOT_FOUND": {"USER": "We can't find <PERSON><PERSON>!", "RECIPIENT": "We can't find <PERSON><PERSON><PERSON><PERSON>!", "CARD": "We can't find <PERSON>!", "EMAIL": "We can't find this email address!", "ACCOUNT": "We can't find this account", "PASSWORD": "We can't find password in this account, Please set password first!", "TRANSACTION": "Please enter valid transaction number", "VERIFICATION": "Your code is no longer valid, Please request again !", "VERIFICATION_DETAILS": "User verification details not found!", "MEDIA": "There is no media.", "DATA": "There is nothing here yet.", "STREAM": "Streaming was deleted.", "SYSTEM": {"VALUE": "There is no system value this slug."}, "FAQ": "We can't find question!"}, "PASSWORD": {"MISMATCH": "Provided password values do not match", "TOO_SIMPLE": "Please create more complicated password", "INCORRECT": "Password incorrect", "SAME": "Old password and new password are same. Please create another password"}, "STATUS": {"DEACTIVATED_ACCOUNT": "The account is not active! Please reach out to our admins.", "NOT_VERIFIED_ACCOUNT": "The account is not verified! Please verify account first.", "INCORRECT_OTP": "Verification code is incorrect"}, "OTP": {"MISMATCH": "The password reset link has already been used or is expired"}, "FILE": {"INVAlID_CSV_FILE": "Invalid csv file", "INVALID_IMAGE_FILE": "Invalid image file", "INVALID_IMAGE_SIZE": "Image size less than 4MB"}, "USER": {"MOBILE_NOT_ALLOW": "This user don't have mobile access", "INVALID_USER_ROLE": "Can't perform this operation", "INVALID_ROLE_ID": "Can't assign user to this role", "EMAIL_ALREADY_EXISTS": "This email already exists use another email"}, "COMMON": {"SKIP": "Skip value must be integer", "LIMIT": "Limit value must be integer", "DATE": "Invalid date format", "DATE_RANGE": "Start and end date must be exists together"}}, "USER": {"REPORT": "User reported successfully", "BLOCK": "User blocked successfully", "UN_BLOCK": "User unblocked successfully", "DELETED": "Account deleted successfully", "PASSWORD": "Your password changed successfully", "RESET_PASSWORD": "Your password reset successfully", "UPDATED": "Updated successfully"}, "VERIFICATION": {"EMAIL": "Email verified successfully"}, "PROCESS": {"VERIFICATION_SENT": "We have sent your verification to the admin.", "OTP_RESENT": "OTP resent successfully", "SENT": {"EMAIL": "We have sent an email to your account"}}, "0/2 users imported successfully": "0/2 users imported successfully", "2/2 users imported successfully": "2/2 users imported successfully", "0/3 users imported successfully": "0/3 users imported successfully", "1/1 users imported successfully": "1/1 users imported successfully"}