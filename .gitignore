# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

#editor settings
.vscode
.prettierrc

#Static images
/Assets/Images/Temp

#logs
/Logs

#Assets

package-lock.json

*.bat
*.sh
