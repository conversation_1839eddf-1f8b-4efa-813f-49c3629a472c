# Use the official Node.js image as the base image
FROM node:20-alpine

# Set the working directory inside the container
WORKDIR /app

# Copy package.json and package-lock.json to the working directory
COPY package*.json ./

# Install the dependencies
RUN npm install

# Copy the rest of the application code to the working directory
COPY . .

COPY .env.staging .env


# Expose the port that your app runs on
EXPOSE 8400

# Start the application
CMD ["node", "server.js"]
